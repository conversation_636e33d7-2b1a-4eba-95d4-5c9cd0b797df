Cypress.Commands.add('createJiraIssue', (testData) => {
  const jiraUrl = 'https://high-level-software.atlassian.net/rest/api/3/issue';
  const jiraProjectKey = 'TA';
  const username = '<EMAIL>';
  const apiKey = 'ATATT3xFfGF0ifSHFMqr3Y9j-ASSQadsMEcAzy_KSkp35nBlZNFjwnW9dJTqPzWaEEZqrqs4umYntn8FECesJxVUrMezM9lOFTOd700nC71rbo96iPPgdHoLlMEukkji-FwCzeC8JxTf4JZqng0R5lVmG1OaM33wFsseh5VMOTTTJ-36oKq8rws=2796F1B3';

  // Get labels from Cypress.env
  const labels = Cypress.env('testFilters') || ['Automation']; // fallback if not set

  const issueData = {
    fields: {
      project: {
        key: jiraPro<PERSON><PERSON>ey
      },
      summary: `Cypress Test failure: ${testData.summary}`,
      description: {
        content: [
          {
            content: [
              {
                text: `The following Cypress test has failed:\n\n${testData.description}`,
                type: 'text'
              }
            ],
            type: 'paragraph'
          }
        ],
        type: 'doc',
        version: 1
      },
      issuetype: {
        name: 'Bug'
      },
      labels // dynamically inserted
      
    }
  };

  console.log('Creating Jira issue with:', issueData);

  cy.request({
    method: 'POST',
    url: jiraUrl,
    body: issueData,
    headers: {
      'Content-Type': 'application/json',
      'accept': 'application/json',
      'Authorization': 'Basic ' + btoa(username + ':' + apiKey)
    }
  }).then((response) => {
    expect(response.status).to.eq(201);
    console.log('Jira issue created successfully:', response.body);
  })
});
