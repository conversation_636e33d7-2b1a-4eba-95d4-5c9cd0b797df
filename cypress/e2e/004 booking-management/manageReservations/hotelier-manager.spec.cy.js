import accounts from '../../../fixtures/accounts';
import TestFilters from '../../../support/filterTests';

TestFilters(['P2Sanity'], () => {

    // NB: These changes modify seeded Reservations
    const Reservations = [
        {
            'name': 'Cypress Guest A',
            'rate': 'Basic',
            'room': 'Single',
            'balance': '£100.00',
            'check_in_time': cy.helpers.dateYMDHIS(),
            'check_out_time': cy.helpers.dateYMDHIfuture(1),
        },
        {
            'name': 'Cypress Guest B',
            'rate': 'Basic',
            'room': 'Double',
            'balance': '£200.00',
            'check_in_time': cy.helpers.dateYMDHIS(),
            'check_out_time': cy.helpers.dateYMDHIfuture(1),
        }
    ]

    describe('Booking Management: Hotelier Manager can edit a Reservation', () => {

        it('can log in', () => {
            cy.fn_login('hotelier', accounts.cypress_a.hotelier.manager.email, accounts.cypress_a.hotelier.manager.password)
            cy.get('.message.success').should('contain', 'Logged in')
        })

        it('has one hotel', () => {
            cy.contains('a', `${accounts.cypress_a.title} Settings`).should('be.visible')
        })

        it('can edit bookings selected from the calendar', () => {
            // Listeners
            cy.intercept({method: 'GET', url: '/hotels/*/calendar/calendar?*'}).as('fetchCalendar');
            cy.intercept({method: 'GET', url: '/hotels/*/calendar/resources?*'}).as('fetchResources');
            cy.intercept({method: 'GET', url: '/hotels/*/calendar/events?*'}).as('fetchEvents');
            cy.intercept({method: 'GET', url: '/hotels/*/calendar/rates?*'}).as('fetchRates');
            cy.intercept({method: 'GET', url: '/hotels/*/calendar/inventory?*'}).as('fetchInventory');
            cy.intercept({method: 'GET', url: '/hotels/*/calendar/credit-cards?*'}).as('fetchCards');
            cy.intercept({method: 'POST', url: '/hotels/*/calendar/book'}).as('createReservation');

            Reservations.forEach(reservation => {

                // Load Calendar
                cy.contains('a', /Calendar/).click().wait(['@fetchResources', '@fetchEvents', '@fetchRates'])

                // Select Booking
                cy.get('a.fc-event').contains(reservation.name).click()

                // Confirm Booking
                cy.url().should('contain', 'bookings')
                cy.get('h2.module__header').should('contain', reservation.name)
                cy.get('.box__details')
                    .should('contain', reservation.name)
                    .and('contain', reservation.rate)
                    .and('contain', reservation.room)
                cy.get('.balance').should('contain', reservation.balance)

                // Select Reservation
                cy.get('.reservation a.edit.fa.fa-pencil').first().click({force: true})
                cy.url().should('contain','reservations').and('contain','edit')
                cy.get('h2.module__header').should('contain', 'Update reservation')
                cy.get('.form__contents input#label').should('contain.value', reservation.name)

                // Edit Reservation Name
                cy.get('.form form').first().fillUpdateReservationForm({
                    'label': reservation.name + ' (edited)'
                }).submit()
                cy.get('.message.success')
                    .should('be.visible')
                    .and('contain', reservation.name)
                    .and('contain', 'updated')

                // Select Reservation
                cy.get('.reservation a.edit.fa.fa-pencil').first().click({force: true})
                cy.url().should('contain','reservations').and('contain','edit')
                cy.get('h2.module__header').should('contain', 'Update reservation')
                cy.get('.form__contents input#label').should('contain.value', reservation.name)

                // Check In Reservation
                cy.get('.form form').first().fillUpdateReservationForm({
                    'checked_in': reservation.check_in_time
                }).submit()
                cy.get('.message.success')
                    .should('be.visible')
                    .and('contain', reservation.name)
                    .and('contain', 'updated')

                // Select Reservation
                cy.get('.reservation a.edit.fa.fa-pencil').first().click({force: true})
                cy.url().should('contain','reservations').and('contain','edit')
                cy.get('h2.module__header').should('contain', 'Update reservation')
                cy.get('.form__contents input#label').should('contain.value', reservation.name)
                cy.get('.form__contents input#checked_in').should('contain.value', reservation.check_in_time)

                // Check Out Booking
                cy.get('.form form').first().fillUpdateReservationForm({
                    'checked_out': reservation.check_out_time
                }).submit()
                cy.get('.message.success')
                    .should('be.visible')
                    .and('contain', reservation.name)
                    .and('contain', 'updated')

                // Select Reservation
                cy.get('.reservation a.edit.fa.fa-pencil').first().click({force: true})
                cy.url().should('contain','reservations').and('contain','edit')
                cy.get('h2.module__header').should('contain', 'Update reservation')
                cy.get('.form__contents input#label').should('contain.value', reservation.name)
                cy.get('.form__contents input#checked_in').should('contain.value', reservation.check_in_time)
                cy.get('.form__contents input#checked_out').should('contain.value', reservation.check_out_time)
                cy.get('.form__contents input#checked_out').should('contain.value', reservation.check_out_time)

                // Cancel Edit Reservation
                cy.contains('a.button', 'Cancel').click()
                cy.url()
                    .should('contain','bookings')
                    .and('not.contain','reservations')
            })
        })

        afterEach(function () {
            cy.fn_afterEachJira(this.currentTest)
        })
    })
})
