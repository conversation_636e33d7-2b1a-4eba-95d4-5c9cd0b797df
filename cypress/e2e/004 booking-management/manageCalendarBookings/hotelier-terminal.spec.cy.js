import accounts from '../../../fixtures/accounts';
import TestFilters from '../../../support/filterTests';

TestFilters(['P2Sanity'], () => {

    describe('Booking Management: Hotelier Terminal cannot create bookings on the calendar', () => {

        it('can not create bookings via the calendar', () => {
            cy.fn_login('hotelier', accounts.cypress_a.hotelier.terminal.email, accounts.cypress_a.hotelier.terminal.password)
            cy.fn_safeVisit('/hotels/cypress-a/calendar');
            cy.get('div.title h2').should('contain.text','Hotelier Log in')
        })

        afterEach(function () {
            cy.fn_afterEachJira(this.currentTest)
        })
    })
})
