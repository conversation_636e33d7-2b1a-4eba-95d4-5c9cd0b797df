import accounts from '../../../fixtures/accounts';
import TestFilters from '../../../support/filterTests';

TestFilters(['P2Sanity'], () => {

    describe('Booking Management: Corporation cannot create bookings on the calendar', () => {

        it('can not create bookings via the calendar', () => {

            cy.fn_login('corporation', accounts.cypress_a.corporation.email, accounts.cypress_a.corporation.password, accounts.cypress_a.slug)
            cy.get('div.title h1').should('contain.text', 'Your Stay')

            cy.fn_safeVisit('/hotels/cypress-a/calendar');
            cy.get('div.title h2').should('contain.text','Hotelier Log in')
        })

        afterEach(function () {
            cy.fn_afterEachJira(this.currentTest)
        })
    })
})
