import accounts from '../../../fixtures/accounts';
import TestFilters from '../../../support/filterTests';

TestFilters(['P2Sanity'], () => {

    describe('Booking Management: Hotelier Terminal cannot edit a Booking', () => {

        it('can log in', () => {
            cy.fn_login('hotelier', accounts.cypress_a.hotelier.terminal.email, accounts.cypress_a.hotelier.terminal.password)
        })

        it('has no access to the calendar', () => {
            cy.fn_safeVisit(`/hotels/${accounts.cypress_a.slug}/calendar`);
            cy.get('div.title h2').should('contain.text','Hotelier Log in')
        })

        it('has no access to bookings', () => {
            cy.fn_safeVisit(`/hotels/${accounts.cypress_a.slug}/bookings`);
            cy.get('div.title h2').should('contain.text','Hotelier Log in')
        })

        afterEach(function () {
            cy.fn_afterEachJira(this.currentTest)
        })
    })
})
