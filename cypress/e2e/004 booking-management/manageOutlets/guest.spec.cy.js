import accounts from '../../../fixtures/accounts';
import TestFilters from '../../../support/filterTests';

TestFilters(['P2Sanity'], () => {

    describe('Booking Management: Guests cannot manage Outlets', () => {

        it('cannot access outlets', () => {
            cy.fn_login('guest', accounts.cypress_a.guest.email, accounts.cypress_a.guest.password, accounts.cypress_a.slug)
            cy.get('header h1').should('contain.text', 'Check availability')

            cy.fn_safeVisit('/hotels/cypress-a/outlets');
            cy.get('div.title h2').should('contain.text','Hotelier Log in')
        })

        afterEach(function () {
            cy.fn_afterEachJira(this.currentTest)
        })
    })
})
