import accounts from '../../../fixtures/accounts';
import vouchers from '../../../fixtures/cypress_a/vouchers';
import TestFilters from '../../../support/filterTests';

TestFilters(['P2Sanity'], () => {

    describe('Booking Management: Hotelier Front Desk can manage Vouchers', () => {

        it('can log in', () => {
            cy.fn_login('hotelier', accounts.cypress_a.hotelier.front_desk.email, accounts.cypress_a.hotelier.front_desk.password)
            cy.get('.message.success').should('contain', 'Logged in')
        })

        it ('can create vouchers', () => {
            // Navigate to Vouchers
            cy.contains('a', /Vouchers/).click({ force: true })
            // Create Vouchers
            vouchers.forEach(voucher => {
                cy.contains('a', /Create voucher/).click()
                cy.get('form').fillCreateVoucherForm(voucher).submit()
                cy.get('.message.success').should('contain', `${voucher.description} Voucher created`)
                cy.get('.voucher-index-table').should('contain', voucher.description.substr(0,10))
            })
        })

        it ('can search vouchers', () => {
            let voucher = vouchers[1]
            cy.contains('a', /Vouchers/).click({ force: true })
            // Search
            cy.get('.col1 form').fillSearchForm({code: voucher.code}).submit()
            // Confirm
            cy.get('.voucher-index-table').should('contain', voucher.code)
        })

        it ('can edit a voucher', () => {
            let voucher = vouchers[1]
            let original_code = voucher.code
            // Select a Voucher to Edit
            cy.contains('a', /Vouchers/).click({ force: true })
            cy.contains('td', original_code).selectEditPencil().click()
            // Update
            cy.get('form').fillUpdateVoucherForm(voucher).submit()
            // Confirm
            cy.get('.voucher-index-table').should('contain', original_code)
        })

        it('can delete a voucher', () => {
            let voucher = vouchers[2]
            // Delete a Voucher
            cy.contains('a', /Vouchers/).click({ force: true })
            cy.contains('td', voucher.code).selectTrashCan().click()
            // Confirm
            cy.get('.message.success').should('contain', voucher.description + ' Voucher deleted')
        })

        afterEach(function () {
            cy.fn_afterEachJira(this.currentTest)
        })
    })
})
