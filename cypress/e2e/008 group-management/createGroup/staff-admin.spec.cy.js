import TestFilters from '../../../support/filterTests';

TestFilters(['P2Sanity'], () => {

    describe('Group Management: Staff Admins', () => {

        it('can log in', () => {
            cy.fn_login('staff', Cypress.env('staff_email'), Cypress.env('staff_password'))
            cy.get('.message.success').should('contain', 'Logged in')

            cy.contains('a', 'Hotels').should('be.visible')
            cy.contains('a', 'Create').should('be.visible')
            cy.contains('a', 'Groups').should('be.visible')
        })

        it('can create a group', () => {

            // Select Groups
            cy.contains('a', 'Groups')
                .click()
                .wait()

            cy.get('.page-title h1').should('be.visible').should('contain.text', 'Group Overview')

            // Add Group
            cy.contains('a', 'Create New Group').should('be.visible')
                .click()
                .wait()

            cy.get('.page-title h1').should('be.visible').should('contain.text', 'Create Group')

        })

        afterEach(function () {
            cy.fn_afterEachJira(this.currentTest)
        })
    })
})
