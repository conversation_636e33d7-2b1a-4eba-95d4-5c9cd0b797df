import accounts from '../../../fixtures/accounts';
import TestFilters from '../../../support/filterTests';

// Room codes cannot be reused when deleted, so need to be different for each role..
const room = {
    'name': 'Del_' + cy.fn_rand(5),
    'roomTypes': ['Single']
}

TestFilters(['P2Sanity'], () => {

    describe('Room Management: Staff Admin can restore a room', () => {

        it('can log in', () => {
            cy.fn_login('staff', Cypress.env('staff_email'), Cypress.env('staff_password'))
            cy.get('.message.success').should('contain', 'Logged in')
        })

        it('can select a hotel', () => {
            cy.get('.col1 form').fillSearchForm({name: accounts.cypress_a.title}).submit()
            cy.get('.module__header').should('contain', accounts.cypress_a.title)
            cy.contains('a', accounts.cypress_a.title).click()
            cy.contains('a', `${accounts.cypress_a.title} Settings`).should('be.visible')
        })

        it('can create a room', () => {
            cy.contains('a', 'Rooms').click({ force: true })
            cy.get('.module__header').should('contain', 'Rooms')
            cy.contains('a', /Create Room Number\/Name/).click()
            cy.get('form').fillCreateRoomForm(room).submit()
            cy.get('.message.success').should('contain', `Room ${room.name} created`)
            cy.url().should('contain', 'room-types')
            // Ensure Room is visible
            cy.get('.room-index-table').should('contain', room.name)
        })

        it('can delete rooms', () => {
            cy.contains('a', /Rooms/).click({ force: true })
            cy.contains('.room-index-table td', room.name).selectTrashCan().click()
            cy.get('.message.success').should('contain', `Room ${room.name} deleted`)
            // Ensure Room is NOT visible
            cy.get('.room-index-table').should('not.contain', room.name)
        })

        it('can restore room', () => {
            cy.contains('a', 'Trash').click({ force: true })
            cy.contains('tr.result', room.name).then($row => {
                cy.get($row).find('form').submit()
            })
            // Ensure Room is visible
            cy.get('.room-index-table').should('contain', room.name)
        })

        afterEach(function () {
            cy.fn_afterEachJira(this.currentTest)
        })
    })
})
