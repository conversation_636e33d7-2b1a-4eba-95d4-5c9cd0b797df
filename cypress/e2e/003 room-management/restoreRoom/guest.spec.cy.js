import accounts from '../../../fixtures/accounts';
import TestFilters from '../../../support/filterTests';

TestFilters(['P2Sanity'], () => {

    describe('Room Management: Guests cannot restore room', () => {

        it('can log in', () => {
            cy.fn_login('guest', accounts.cypress_a.guest.email, accounts.cypress_a.guest.password, accounts.cypress_a.slug)
            cy.get('div.title h1').should('contain.text', 'Your Stay')
        })

        it('cannot restore a room type', () => {
            cy.fn_safeVisit(`/hotels/${accounts.cypress_a.slug}/trash`);
            cy.get('.module__header').should('contain.text','Access Denied')
        })

        afterEach(function () {
            cy.fn_afterEachJira(this.currentTest)
        })
    })
})
