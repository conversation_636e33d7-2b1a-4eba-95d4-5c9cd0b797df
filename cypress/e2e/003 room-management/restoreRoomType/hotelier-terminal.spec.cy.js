import accounts from '../../../fixtures/accounts';
import TestFilters from '../../../support/filterTests';

TestFilters(['P2Sanity'], () => {

    describe('Room Management: Hotelier Terminal can not restore a room type', () => {

        it('can not restore a room type', () => {
            cy.fn_login('hotelier', accounts.cypress_a.hotelier.terminal.email, accounts.cypress_a.hotelier.terminal.password)
            cy.fn_safeVisit(`/hotels/${accounts.cypress_a.slug}/room-types`);
            cy.get('div.title h2').should('contain.text','Hotelier Log in')
            cy.fn_safeVisit(`/hotels/${accounts.cypress_a.slug}/trash`);
            cy.get('.module__header').should('contain.text','Access Denied')
        })

        afterEach(function () {
            cy.fn_afterEachJira(this.currentTest)
        })
    })
})
