import accounts from '../../../fixtures/accounts';
import TestFilters from '../../../support/filterTests';

const roomType = {
    'name': 'Cypress_Y',
    'code': 'cyp_y',
    'letter': 'Y',
    'color': '000000'
}

TestFilters(['P2Sanity'], () => {

    describe('Room Management: Hotelier Manager can restore a room type', () => {

        it('can log in', () => {
            cy.fn_login('hotelier', accounts.cypress_a.hotelier.manager.email, accounts.cypress_a.hotelier.manager.password)
            cy.get('.message.success').should('contain', 'Logged in')
        })

        it('can create a room type', () => {
            cy.contains('a', 'Rooms').click({ force: true })
            cy.get('.module__header').should('contain', 'Room Types')
            cy.contains('a', 'Create Room Type').click()
            cy.get('form').fillCreateRoomTypeForm(roomType).submit()
            cy.get('.message.success').should('contain', `${roomType.name} created`)
            // Ensure RoomType is visible
            cy.get('.room-type-index-table').should('contain', roomType.name)
        })

        it('can delete room type', () => {
            cy.contains('a', 'Rooms').click({ force: true })
            cy.get('.module__header').should('contain', 'Room Types')
            cy.contains('.room-type-index-table td', roomType.name).selectTrashCan().click()
            cy.get('.message.success').should('contain', `${roomType.name} deleted`)
            // Ensure RoomType is NOT visible
            cy.get('.room-type-index-table').should('not.contain', roomType.name)
        })

        it('can restore room type', () => {
            cy.contains('a', 'Trash').click({ force: true })
            cy.contains('tr.result', roomType.code).then($row => {
                cy.get($row).find('form').submit()
            })
            // Ensure RoomType is visible
            cy.get('.room-type-index-table').should('contain', roomType.name)
        })

        afterEach(function () {
            cy.fn_afterEachJira(this.currentTest)
        })

    })
})
