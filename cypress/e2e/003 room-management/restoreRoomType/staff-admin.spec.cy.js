import accounts from '../../../fixtures/accounts';
import TestFilters from '../../../support/filterTests';

const roomType = {
    'name': 'Cypress_X',
    'code': 'cyp_x',
    'letter': 'X',
    'color': '000000'
}

TestFilters(['P2Sanity'], () => {

    describe('Room Management: Staff Admin can restore a room type', () => {

        it('can log in', () => {
            cy.fn_login('staff', Cypress.env('staff_email'), Cypress.env('staff_password'))
            cy.get('.message.success').should('contain', 'Logged in')
        })

        it('can select a hotel', () => {
            cy.get('.col1 form').fillSearchForm({name: accounts.cypress_a.title}).submit()
            cy.get('.module__header').should('contain', accounts.cypress_a.title)
            cy.contains('a', accounts.cypress_a.title).click()
            cy.contains('a', `${accounts.cypress_a.title} Settings`).should('be.visible')
        })

        it('can create a room type', () => {
            cy.contains('a', 'Rooms').click({ force: true })
            cy.get('.module__header').should('contain', 'Room Types')
            cy.contains('a', 'Create Room Type').click()
            cy.get('form').fillCreateRoomTypeForm(roomType).submit()
            cy.get('.message.success').should('contain', `${roomType.name} created`)
            // Ensure RoomType is visible
            cy.get('.room-type-index-table').should('contain', roomType.name)
        })

        it('can delete room type', () => {
            cy.contains('a', 'Rooms').click({ force: true })
            cy.get('.module__header').should('contain', 'Room Types')
            cy.contains('.room-type-index-table td', roomType.name).selectTrashCan().click()
            cy.get('.message.success').should('contain', `${roomType.name} deleted`)
            // Ensure RoomType is NOT visible
            cy.get('.room-type-index-table').should('not.contain', roomType.name)
        })

        it('can restore room type', () => {
            cy.contains('a', 'Trash').click({ force: true })
            cy.contains('tr.result', roomType.code).then($row => {
                cy.get($row).find('form').submit()
            })
            // Ensure RoomType is visible
            cy.get('.room-type-index-table').should('contain', roomType.name)
        })

        afterEach(function () {
            cy.fn_afterEachJira(this.currentTest)
        })
    })
})
