import accounts from '../../../fixtures/accounts';
import TestFilters from '../../../support/filterTests';

TestFilters(['P2Sanity'], () => {

    describe('Room Management: Corporations can not create room types', () => {

        it('can log in', () => {
            cy.fn_login('corporation', accounts.cypress_a.corporation.email, accounts.cypress_a.corporation.password, accounts.cypress_a.slug)
            cy.get('h1').should('contain.text', 'Your Stay')
        })

        it('can not create a room type', () => {
            cy.fn_safeVisit(`/hotels/${accounts.cypress_a.slug}/room-types`);
            cy.get('div.title h2').should('contain.text','Hotelier Log in')
        })

        afterEach(function () {
            cy.fn_afterEachJira(this.currentTest)
        })
    })
})
