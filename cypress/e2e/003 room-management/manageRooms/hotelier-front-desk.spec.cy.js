import accounts from '../../../fixtures/accounts';
import TestFilters from '../../../support/filterTests';

// Room Type codes cannot be reused when deleted, so need to be different for each role..
const rooms = [
    {   'name': 'Cy_1_HFD',  'directions': 'Up stairs, on the left',     'roomTypes': ['Single']   },
    {   'name': 'Cy_2_HFD',  'directions': 'Up stairs, on the right',    'roomTypes': ['Double']   },
    {   'name': 'Cy_3_HFD',  'directions': 'Up stairs, end of corridor', 'roomTypes': ['Double', 'Luxury']   },
]
TestFilters(['P2Sanity'], () => {

    describe('Room Management: Hotelier Front Desk can create a room', () => {

        it('can log in', () => {
            cy.fn_login('hotelier', accounts.cypress_a.hotelier.front_desk.email, accounts.cypress_a.hotelier.front_desk.password)
            cy.get('.message.success').should('contain', 'Logged in')
        })

        it('can create a room', () => {
            cy.contains('a', 'Rooms').click({ force: true })
            cy.get('.module__header').should('contain', 'Rooms')
            rooms.forEach(room => {
                cy.contains('a', /Create Room Number\/Name/).click()
                cy.get('form').fillCreateRoomForm(room).submit()
                cy.get('.message.success').should('contain', `Room ${room.name} created`)
                cy.url().should('contain', 'room-types')
            })
        })

        it('can delete rooms', () => {
            cy.contains('a', /Rooms/).click({ force: true })
            rooms.forEach(room => {
                cy.contains('.room-index-table td', room.name).selectTrashCan().click()
                cy.get('.message.success').should('contain', `Room ${room.name} deleted`)
            })
        })
        afterEach(function () {
            cy.fn_afterEachJira(this.currentTest)
        })
    })
})
