import accounts from '../../../fixtures/accounts';
import rateDeposits from '../../../fixtures/cypress_a/rate_deposits'
import TestFilters from '../../../support/filterTests';

TestFilters(['P2Sanity'], () => {

    describe('Plan Management: Staff Admin can create rate deposits', () => {

        it('can log in', () => {
            cy.fn_login('staff', Cypress.env('staff_email'), Cypress.env('staff_password'))
            cy.get('.message.success').should('contain', 'Logged in')
        })

        it('can select a hotel', () => {
            cy.get('.col1 form').fillSearchForm({name: accounts.cypress_a.title}).submit()
            cy.get('.module__header').should('contain', accounts.cypress_a.title)
            cy.contains('a', accounts.cypress_a.title).click()
            cy.contains('a', `${accounts.cypress_a.title} Settings`).should('be.visible')
        })

        it('can configure deposit rates', () => {

            // Setup Listeners
            cy.intercept({method: 'GET', url: '/hotels/*/deposit-rates/ajax-summary?*'}).as('getDepositRates');

            // Navigate to Deposit Rates
            cy.contains('a', 'Deposit Rates').click({ force: true })
                .wait(['@getDepositRates'])

            // Set Base Rates
            cy.get('.body > form').first().fillBaseRatesForm(rateDeposits.base_rates).within($form => {
                cy.get($form).contains('button', 'Save').click()
            })
            cy.get('.message.success').should('contain', 'Hotel base deposit rate updated successfully')

            // Update Rates per Date
            cy.get('.box.table.fields form').fillRatesPerDateForm(rateDeposits.rates_per_date).within($form => {
                cy.get($form).find('input.button').click()
            })
            cy.get('.message.success').should('contain', 'Hotel base deposit rate updated successfully')

            // Check Rate Overrides table view
            cy.wait('@getDepositRates').wait(2000)
            cy.get('table#rates-summary tbody tr').first().then($row=> {
                cy.get($row).find('td').eq(1).should('contain.text', rateDeposits.rates_per_date.thresholds)
                cy.get($row).find('td').eq(2).should('contain.text', rateDeposits.rates_per_date.amounts)
                cy.get($row).find('td').eq(3).should('contain.text', rateDeposits.rates_per_date.amountStrategies)
                cy.get($row).find('td').eq(4).should('contain.text', rateDeposits.rates_per_date.chargeableStrategies)
                cy.get($row).find('td').eq(5).should('contain.text', rateDeposits.rates_per_date.descriptions)
            })

            // Bulk Override
            cy.get('.container--two-small-halves').parent('form').fillRatesBulkUpdateForm(rateDeposits.bulk_update).within($form => {
                cy.get($form).find('button.button').click()
            })
            cy.get('.message.success').should('contain', 'Hotel rates have been updated')

            // Check Bulk Update is visible for start_date
            cy.wait('@getDepositRates').wait(2000)
            cy.get('table#rates-summary tbody tr').first().then($row=> {
                cy.get($row).find('td').eq(1).should('contain.text', rateDeposits.bulk_update.threshold)
                cy.get($row).find('td').eq(2).should('contain.text', rateDeposits.bulk_update.amount)
                cy.get($row).find('td').eq(3).should('contain.text', rateDeposits.bulk_update.amountStrategy)
                cy.get($row).find('td').eq(4).should('contain.text', rateDeposits.bulk_update.chargeableStrategy)
                cy.get($row).find('td').eq(5).should('contain.text', rateDeposits.bulk_update.description)
            })
        })

        afterEach(function () {
            cy.fn_afterEachJira(this.currentTest)
        })
    })
})
