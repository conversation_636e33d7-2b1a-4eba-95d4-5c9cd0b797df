import accounts from '../../../fixtures/accounts';
import TestFilters from '../../../support/filterTests';

TestFilters(['P2Sanity'], () => {

    describe('Plan Management: Guests cannot create a rate hurdle', () => {

        it('cannot access rates', () => {
            cy.fn_login('guest', accounts.cypress_a.guest.email, accounts.cypress_a.guest.password, accounts.cypress_a.slug)
            cy.get('header h1').should('contain.text', 'Check availability')
        })

        it('cannot create a rate', () => {
            cy.fn_safeVisit('hotels/cypress-a/v2/rates/hurdle')
            cy.get('.module__header').should('contain.text','Page not found')
        })

        afterEach(function () {
            cy.fn_afterEachJira(this.currentTest)
        })
    })
})
