import accounts from '../../../fixtures/accounts';
import TestFilters from '../../../support/filterTests';

TestFilters(['P2Sanity'], () => {

    describe('Plan Management: Hotelier Front Desk cannot create a rate hurdle', () => {

        it('can log in', () => {
            cy.fn_login('hotelier', accounts.cypress_a.hotelier.front_desk.email, accounts.cypress_a.hotelier.front_desk.password)
            cy.get('.message.success').should('contain', 'Logged in')
        })

        it('cannot create a rate hurdle', () => {
            // Set up listeners
            cy.intercept({method: 'GET', url: '/permissions'}).as('fetchPermissions');
            cy.intercept({method: 'POST', url: '/hotels/search'}).as('fetchHotels');
            cy.intercept({method: 'POST', url: '/roomTypes/search'}).as('fetchRoomTypes');
            cy.intercept({method: 'POST', url: '/hurdles/'}).as('fetchRateHurdles');
            cy.intercept({method: 'POST', url: '/hurdles/create'}).as('createRateHurdle');
            cy.intercept({method: 'POST', url: '/rates'}).as('fetchRates');
            cy.contains('a', 'Rate Hurdles').click({ force: true })
                .wait(['@fetchPermissions', '@fetchHotels', '@fetchRoomTypes', '@fetchRateHurdles'])
            cy.contains('a', 'Create New Hurdle').should('not.exist')
        })

        afterEach(function () {
            cy.fn_afterEachJira(this.currentTest)
        })
    })
})