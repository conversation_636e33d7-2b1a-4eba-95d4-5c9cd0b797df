import accounts from '../../../fixtures/accounts';
import TestFilters from '../../../support/filterTests';

TestFilters(['P2Sanity'], () => {

    describe('Plan Management: Hotelier Terminal cannot create a derived rate', () => {

        it('cannot access rates', () => {
            cy.fn_login('hotelier', accounts.cypress_a.hotelier.terminal.email, accounts.cypress_a.hotelier.terminal.password)
        })

        it('cannot create a rate', () => {
            cy.fn_safeVisit('hotels/cypress-a/v2/rates/create')
            cy.get('.module__header').should('contain.text','Page not found')
        })

        afterEach(function () {
            cy.fn_afterEachJira(this.currentTest)
        })
    })
})
