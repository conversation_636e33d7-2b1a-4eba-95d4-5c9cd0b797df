import accounts from '../../../fixtures/accounts';
import TestFilters from '../../../support/filterTests';

TestFilters(['P2Sanity'], () => {

    const derivedRate = {
        'rate_name': 'Basic',
        'status': 'Derived',
        'derived_from': 'Double(dbl) / Basic',
        'plus_minus': 'minus',
        'strategy': 'Amount',
        'value': 30
    }

    describe('Plan Management: Hotelier Manager can create a derived rate', () => {

        it('can log in', () => {
            cy.fn_login('hotelier', accounts.cypress_a.hotelier.manager.email, accounts.cypress_a.hotelier.manager.password)
            cy.get('.message.success').should('contain', 'Logged in')
        })

        it('has one hotel', () => {
            cy.contains('a', `${accounts.cypress_a.title} Settings`).should('be.visible')
        })

        it('can create a derived rate', () => {
            // Set up listeners
            cy.intercept({method: 'GET', url: '/permissions'}).as('fetchPermissions');
            cy.intercept({method: 'POST', url: '/hotels/search'}).as('fetchHotels');
            cy.intercept({method: 'POST', url: '/roomTypes/search'}).as('fetchRoomTypes');
            cy.intercept({method: 'POST', url: '/rates'}).as('fetchRates');
            cy.intercept({method: 'POST', url: '/derivations/create/*'}).as('createDerivation');
            // Navigate to Rate Plans
            cy.contains('a', /Rate Plans/).click({ force: true })
                .wait(['@fetchPermissions', '@fetchHotels', '@fetchRoomTypes'])
            // Create Derived Rates
            cy.contains('p', derivedRate.rate_name).click()
            // Select Derivations
            cy.contains('a', /Derivations/).click()
                .wait(1000) // wait for animation to complete
            // or, if the Rate is Derived, a RoomType search
            // Manual / Derived toggle
            cy.get('.config-toggle [role="button"]').first().then((element) => {
                // Class 'off' == 'Derived'
                if (derivedRate.status === 'Manual'  && element.hasClass('off') ||
                    derivedRate.status === 'Derived' && (!element.hasClass('off'))) {
                    cy.get(element).click();
                }
            })
            if (derivedRate.status === 'Derived') {
                // Set Derivation config
                cy.wait(1000).get('.derivation-form-wrapper form').fillDerivationForm({
                    derived_from: derivedRate.derived_from,
                    plus_minus: derivedRate.plus_minus,
                    strategy: derivedRate.strategy,
                    value: derivedRate.value
                })
            }
            // Save Rate Derivation
            cy.get('.modal-footer > .primary').click().wait(['@createDerivation', '@fetchRates'])
            cy.url().should('contain', '/v2/rates/overview')
        })

        afterEach(function () {
            cy.fn_afterEachJira(this.currentTest)
        })
    })
})
