import TestFilters from '../../support/filterTests';
import accounts from '../../fixtures/accounts';

const date = cy.helpers.dateYMD();
const hotelSlug = accounts.cypress_a.slug

const pages = [
    { group: 'pageGroupOne', name: 'Housekeeping',              url: `/hotels/${hotelSlug}/housekeeping` },
    { group: 'pageGroupOne', name: 'Staff',                     url: `/hotels/${hotelSlug}/hoteliers` },
    { group: 'pageGroupOne', name: 'Gallery',                   url: `/hotels/${hotelSlug}/images/create` },
    { group: 'pageGroupOne', name: 'Emails',                    url: `/hotels/${hotelSlug}/emails` },
    { group: 'pageGroupOne', name: 'Em<PERSON> Sent Log',           url: `/hotels/${hotelSlug}/emails/sent` },
    { group: 'pageGroupOne', name: 'Calendar',                  url: `/hotels/${hotelSlug}/calendar?date=${date}` },
    { group: 'pageGroupOne', name: 'Promotional Codes',         url: `/hotels/${hotelSlug}/promotions` },
    { group: 'pageGroupOne', name: 'Vouchers',                  url: `/hotels/${hotelSlug}/vouchers?date=${date}` },
    { group: 'pageGroupOne', name: 'Rate Hurdles',              url: `/hotels/${hotelSlug}/v2/rates/hurdle` },
    { group: 'pageGroupOne', name: 'Policies',                  url: `/hotels/${hotelSlug}/policies` },
    { group: 'pageGroupOne', name: 'Packages',                  url: `/hotels/${hotelSlug}/packages?date=${date}` },
    { group: 'pageGroupOne', name: 'Rate Plans',                url: `/hotels/${hotelSlug}/v2/rates/overview` },
    { group: 'pageGroupOne', name: 'Grid',                      url: `/hotels/${hotelSlug}/v2/rates/grid` },
    { group: 'pageGroupOne', name: 'Categories',                url: `/hotels/${hotelSlug}/categories` },
    { group: 'pageGroupOne', name: 'Products',                  url: `/hotels/${hotelSlug}/products` },
    { group: 'pageGroupOne', name: 'Rewards',                   url: `/hotels/${hotelSlug}/rewards` },
    { group: 'pageGroupOne', name: 'Rooms',                     url: `/hotels/${hotelSlug}/room-types` },
    { group: 'pageGroupOne', name: 'Venues',                    url: `/hotels/${hotelSlug}/venues` },
    { group: 'pageGroupOne', name: 'Outlets',                   url: `/hotels/${hotelSlug}/outlets` },
    { group: 'pageGroupOne', name: 'Bookings',                  url: `/hotels/${hotelSlug}/bookings` },
    { group: 'pageGroupOne', name: 'Assign Reservations',       url: `/hotels/${hotelSlug}/bookings/unassigned` },
    { group: 'pageGroupOne', name: 'Guests',                    url: `/hotels/${hotelSlug}/guests` },
    { group: 'pageGroupOne', name: 'Corporations',              url: `/hotels/${hotelSlug}/corporations` },
    { group: 'pageGroupOne', name: 'Reports',                   url: `/hotels/${hotelSlug}/v2/reports/overview` },
    { group: 'pageGroupOne', name: 'Audit Report',              url: `/hotels/${hotelSlug}/reports/audit?date=${date}` },
    { group: 'pageGroupOne', name: 'Sales Report',              url: `/hotels/${hotelSlug}/reports/sales?date=${date}` },
    { group: 'pageGroupOne', name: 'Daily Sales Report Legacy', url: `/hotels/${hotelSlug}/reports/daily` },
    { group: 'pageGroupOne', name: 'Daily Sales Report',        url: `/hotels/${hotelSlug}/v2/reports/daily-sales` },
    { group: 'pageGroupOne', name: 'Weekly Sales Report',       url: `/hotels/${hotelSlug}/reports/weekly?date=${date}` },
    { group: 'pageGroupOne', name: 'Monthly Sales Report',      url: `/hotels/${hotelSlug}/reports/monthly?date=${date}` },
    { group: 'pageGroupOne', name: 'Postings Report',           url: `/hotels/${hotelSlug}/reports/posting?date=${date}` },
    { group: 'pageGroupOne', name: 'Purchase Report',           url: `/hotels/${hotelSlug}/reports/posting/purchases?date=${date}` },
    { group: 'pageGroupOne', name: 'Outlet Report',             url: `/hotels/${hotelSlug}/reports/posting/outlets?date=${date}` },
    { group: 'pageGroupOne', name: 'Ledger Report',             url: `/hotels/${hotelSlug}/reports/ledger?date=${date}` },
    { group: 'pageGroupOne', name: 'Creditor Report',           url: `/hotels/${hotelSlug}/reports/ledger/creditor?date=${date}` },
    { group: 'pageGroupOne', name: 'Debtor Report',             url: `/hotels/${hotelSlug}/reports/ledger/debtor?date=${date}` },
    { group: 'pageGroupOne', name: 'Owed Report',               url: `/hotels/${hotelSlug}/reports/ledger/owed?date=${date}` },
    { group: 'pageGroupOne', name: 'Booking Report',            url: `/hotels/${hotelSlug}/reports/booking?date=${date}` },
    { group: 'pageGroupOne', name: 'Client Report',             url: `/hotels/${hotelSlug}/reports/booking/client?date=${date}` },
    { group: 'pageGroupOne', name: 'Referrer Report',           url: `/hotels/${hotelSlug}/reports/booking/referrer?date=${date}` },
    { group: 'pageGroupOne', name: 'Room Type Report',          url: `/hotels/${hotelSlug}/reports/booking/room-type?date=${date}` },
    { group: 'pageGroupOne', name: 'Cards Report',              url: `/hotels/${hotelSlug}/reports/booking/cards?date=${date}` },
    { group: 'pageGroupOne', name: 'Cancellation Report',       url: `/hotels/${hotelSlug}/reports/booking/cancellation?date=${date}` },
    { group: 'pageGroupOne', name: 'Channel Report',            url: `/hotels/${hotelSlug}/reports/reservation/channel?date=${date}` },
    { group: 'pageGroupOne', name: 'Invoice Report',            url: `/hotels/${hotelSlug}/reports/invoice?date=${date}` },
    { group: 'pageGroupOne', name: 'Payment Report',            url: `/hotels/${hotelSlug}/reports/invoice/payment?date=${date}` },
    { group: 'pageGroupOne', name: 'Deposits Report',           url: `/hotels/${hotelSlug}/reports/invoice/deposit?date=${date}` },
    { group: 'pageGroupOne', name: 'Occupancy Report',          url: '/hotels/'+ hotelSlug + '/v2/reports/occupancy'},
    { group: 'pageGroupOne', name: 'Occupancy Report Legacy',   url: '/hotels/'+ hotelSlug +'/reports/occupancy'},
    { group: 'pageGroupOne', name: 'Availability Report',       url: '/hotels/'+ hotelSlug +`/reports/occupancy/availability?date=${date}`},
    { group: 'pageGroupOne', name: 'Activity Report',           url: '/hotels/'+ hotelSlug +`/reports/occupancy/activity?date=${date}`},
    { group: 'pageGroupOne', name: 'Lead Time Report',          url: '/hotels/'+ hotelSlug +`/reports/occupancy/lead-time?date=${date}`},
    { group: 'pageGroupOne', name: 'Block Report',              url: '/hotels/'+ hotelSlug +`/reports/occupancy/block?date=${date}`},
    { group: 'pageGroupOne', name: 'Fire Safety Report',        url: '/hotels/'+ hotelSlug +`/reports/fire-safety?date=${date}`},
    { group: 'pageGroupOne', name: 'Guest Report',              url: '/hotels/'+ hotelSlug +`/reports/guest?date=${date}`},
    { group: 'pageGroupOne', name: 'Corporation Report',        url: '/hotels/'+ hotelSlug +`/reports/corporation?date=${date}`},
    { group: 'pageGroupOne', name: 'Marketing Report',          url: '/hotels/'+ hotelSlug +`/reports/marketing?date=${date}`},
    { group: 'pageGroupTwo', name: 'Product Rates',             url: `/hotels/${hotelSlug}/products/rateByDate` },
    { group: 'pageGroupTwo', name: 'Reward Rates',              url: `/hotels/${hotelSlug}/rewards/rateByDate` }
];

TestFilters(['P1Sanity MVP'], () => {

    describe('Terminal : MVP visit pages', () => {

        beforeEach('Handle uncaught exceptions and log in as terminal', () => {
            cy.clearCookies();
            cy.clearLocalStorage();
            cy.on('uncaught:exception', (e) => e.message.includes("Cannot set properties of undefined (setting '_DT_CellIndex')") && false);
            cy.intercept('POST', 'https://region1.google-analytics.com/g/collect', { statusCode: 204 }).as('analyticsCall');
            cy.fn_login('hotelier', accounts.cypress_a.hotelier.terminal.email, accounts.cypress_a.hotelier.terminal.password)
        })


        it('Attempts to visit Dashboard', () => {
            let url = '/hotels/' + hotelSlug + `?date=${date}`
            cy.fn_safeVisit(url)
            cy.url().should('include', `/${hotelSlug}/availability`)
            cy.request(url).its('status').should('equal', 200);
        })

        it('Attempts to visit Availability', () => {
            let url = '/hotels/'+ hotelSlug +'/availability'
            cy.fn_safeVisit(url)
            cy.url().should('include', url)
            cy.request(url).its('status').should('equal', 200);
        })

        it('Attempts to visit Trash', () => {
            let url =  '/hotels/'+ hotelSlug +'/trash'
            cy.fn_safeVisit(url)
            cy.url().should('include', url)
            cy.request({method: 'GET', url, failOnStatusCode: false}).its('status').should('equal', 403);
        })

        pages.forEach(({ group, name, url }) => {
            it(`Visits the ${name} page`, () => {
                cy.fn_safeVisit(url);
                cy.url().should('include', group === 'pageGroupOne' ? '/login' : url);
                cy.request({ method: 'GET', url, failOnStatusCode: false })
                    .its('status')
                    .should('equal', group === 'pageGroupOne' ? 200 : 500);
            });
        });

        afterEach(function () {
            cy.fn_afterEachJira(this.currentTest)
        })
    })
})