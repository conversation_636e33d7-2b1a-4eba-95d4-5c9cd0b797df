import accounts from '../../../fixtures/accounts';
import TestFilters from '../../../support/filterTests';

TestFilters(['P2Sanity'], () => {

    describe('Front Desk: View Booking Summary as a Guest', () => {

        it('can log in', () => {
            cy.fn_login('guest', accounts.cypress_a.guest.email, accounts.cypress_a.guest.password, accounts.cypress_a.slug)
            cy.contains('button', 'Login').should('not.exist')
            cy.contains('a.button.default', 'My Account').should('be.visible')
        })

        it('cannot access booking summary', () => {
            cy.fn_safeVisit('/hotels/' + accounts.cypress_a.slug + '/bookings') //TODO: add /<booking_ref>
            cy.get('body').should('contain.text', 'Check availability')
            cy.contains('button', 'Login').should('be.visible')
        })

        afterEach(function () {
            cy.fn_afterEach<PERSON>ira(this.currentTest)
        })
    })
})
