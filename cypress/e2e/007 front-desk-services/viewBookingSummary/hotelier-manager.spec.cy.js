import accounts from '../../../fixtures/accounts';
import TestFilters from '../../../support/filterTests';

TestFilters(['P2Sanity'], () => {

    describe('Front Desk: View Booking Summary as Hotelier Manager', () => {

        it('can log in', () => {
            cy.fn_login('hotelier', accounts.cypress_a.hotelier.manager.email, accounts.cypress_a.hotelier.manager.password)
            cy.get('.message.success').should('contain', 'Logged in')
        })

        it('can view booking summary', () => {
            cy.fn_safeVisit('/hotels/' + accounts.cypress_a.slug + '/bookings')
            cy.get(':nth-child(1) > .sorting_1 > a').click();
            cy.get('body').should('contain.text', 'Booking created by Seeding')
        })

        afterEach(function () {
            cy.fn_afterEachJira(this.currentTest)
        })
    })
})
