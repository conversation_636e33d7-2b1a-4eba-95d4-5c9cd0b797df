import accounts from '../../../fixtures/accounts';
import TestFilters from '../../../support/filterTests';

TestFilters(['P2Sanity'], () => {

    describe('Front Desk: View Dashboard as Staff Admin', () => {

        it('can log in', () => {
            cy.fn_login('staff', Cypress.env('staff_email'), Cypress.env('staff_password'))
            cy.get('.message.success').should('contain', 'Logged in')
        })

        it('can view dashboard', () => {
            cy.fn_safeVisit('/hotels/' + accounts.cypress_a.slug)
            cy.get('body').should('contain.text', 'Actions')
            cy.get('body').should('contain.text', 'Show products due')
            cy.get('body').should('contain.text', 'Show Availability')
            cy.contains('a', 'View Activity Report').should('exist')
            cy.contains('a', 'View Housekeeping').should('exist')
            cy.contains('a', 'Print Registration Forms').should('exist')
            cy.contains('a', 'Print Invoices').should('exist')
        })

        afterEach(function () {
            cy.fn_afterEachJira(this.currentTest)
        })
    })
})
