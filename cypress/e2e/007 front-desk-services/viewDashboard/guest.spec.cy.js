import accounts from '../../../fixtures/accounts';
import TestFilters from '../../../support/filterTests';

TestFilters(['P2Sanity'], () => {

    describe('Front Desk: View Dashboard as a Guest', () => {

        it('can log in', () => {
            cy.fn_login('guest', accounts.cypress_a.guest.email, accounts.cypress_a.guest.password, accounts.cypress_a.slug)
            cy.contains('button', 'Login').should('not.exist')
            cy.contains('a.button.default', 'My Account').should('be.visible')
        })

        it('cannot access dashboard', () => {
            cy.fn_safeVisit('/hotels/' + accounts.cypress_a.slug)
            cy.get('body').should('contain.text', 'Check availability')
            cy.contains('button', 'Login').should('be.visible')
        })

        afterEach(function () {
            cy.fn_afterEachJira(this.currentTest)
        })
    })
})
