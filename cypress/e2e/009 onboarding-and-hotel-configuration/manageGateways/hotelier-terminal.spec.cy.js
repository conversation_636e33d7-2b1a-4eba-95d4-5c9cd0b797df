import accounts from '../../../fixtures/accounts';
import TestFilters from '../../../support/filterTests';

TestFilters(['P2Sanity'], () => {

    describe('On-Boarding: Terminal accounts cannot create a payment gateway', () => {

        it('can log in', () => {
            cy.fn_login('hotelier', accounts.cypress_a.hotelier.terminal.email, accounts.cypress_a.hotelier.terminal.password)
        })

        it('cannot create a payment gateway', () => {
            cy.fn_safeVisit('/hotels/cypress-a/edit');
            cy.get('.module__header').should('contain.text','Access Denied')
        })

        afterEach(function () {
            cy.fn_afterEachJira(this.currentTest)
        })
    })
})
