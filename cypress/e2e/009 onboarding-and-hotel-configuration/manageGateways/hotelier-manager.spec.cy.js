import accounts from '../../../fixtures/accounts';
import gateways from '../../../fixtures/cypress_a/gateways'
import TestFilters from '../../../support/filterTests';

TestFilters(['P2Sanity'], () => {

    describe('Onboarding: Hotelier Manager can create a payment gateway', () => {

        it('can log in', () => {
            cy.fn_login('hotelier', accounts.cypress_a.hotelier.manager.email, accounts.cypress_a.hotelier.manager.password)
            cy.get('.message.success').should('contain', 'Logged in')
        })

        it('has one hotel', () => {
            cy.contains('a', `${accounts.cypress_a.title} Settings`).should('be.visible')
        })

        it('can create a gateway', () => {
            // Requests to watch
            cy.intercept({method: 'GET', url: '/hotels/*/ajax-gateways'}).as('fetchHotelGateways')
            cy.intercept({method: 'POST', url: '/gateways/list'}).as('fetchGatewaysList')
            cy.intercept({method: 'POST', url: '/hotels/*/ajax-gateway'}).as('saveHotelGateway')
            cy.intercept({method: 'GET', url: '/gateways/*',}).as('fetchGateway')
            // Wait for Requests to load
            cy.contains('a', `${accounts.cypress_a.title} Settings`).click()
                .wait(['@fetchHotelGateways', '@fetchGatewaysList']).wait(2000)
            // Add Gateway
            cy.get('button.add-gateway').click()
                .wait('@fetchGateway').wait(2000)
            // Get Gateway Form
            cy.get('.gateways .merchant').filter(':visible').last().fillCreateGatewayForm({
                gateway: gateways.judopay
            })
            // Confirm
            cy.get('.gateway-success').should('contain', 'Merchant saved successfully')
            // Make Primary
            cy.selectPrimaryGateway(gateways.judopay.name)
        })

        it('can edit a gateway', () => {
            // Requests to watch
            cy.intercept({method: 'GET', url: '/hotels/*/ajax-gateways'}).as('fetchHotelGateways')
            cy.intercept({method: 'POST', url: '/gateways/list'}).as('fetchGatewaysList')
            cy.intercept({method: 'POST', url: '/hotels/*/ajax-gateway'}).as('saveHotelGateway')
            cy.intercept({method: 'GET', url: '/gateways/*',}).as('fetchGateway')
            // Wait for Requests to load
            cy.contains('a', `${accounts.cypress_a.title} Settings`).click()
                .wait(['@fetchHotelGateways', '@fetchGatewaysList', '@fetchGateway']).wait(2000)
            // Edit Gateway
            cy.get('.gateways .merchant').last().fillUpdateGatewayForm('Updated description')
            cy.get('.gateway-success').should('contain', 'Merchant saved successfully')
        })

        afterEach(function () {
            cy.fn_afterEachJira(this.currentTest)
        })
    })
})
