import accounts from '../../../fixtures/accounts';
import TestFilters from '../../../support/filterTests';

TestFilters(['P2Sanity'], () => {

    describe('Onboarding: Hotelier Front Desk cannot create a payment gateway', () => {

        it('can log in', () => {
            cy.fn_login('hotelier', accounts.cypress_a.hotelier.front_desk.email, accounts.cypress_a.hotelier.front_desk.password)
            cy.get('.message.success').should('contain', 'Logged in')
        })

        it('cannot edit hotel settings', () => {
            cy.fn_safeVisit('/hotels/cypress-a/edit');
            cy.get('.module__header').should('contain.text','Access Denied')
        })

        afterEach(function () {
            cy.fn_afterEachJira(this.currentTest)
        })
    })
})
