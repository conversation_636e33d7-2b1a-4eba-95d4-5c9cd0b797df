import accounts from '../../../fixtures/accounts';
import TestFilters from '../../../support/filterTests';

TestFilters(['P2Sanity'], () => {

    describe('On-Boarding: Hotelier Terminal cannot create a hoteliers', () => {

        it('can log in', () => {
            cy.fn_login('hotelier', accounts.cypress_a.hotelier.terminal.email, accounts.cypress_a.hotelier.terminal.password)
        })

        it('can not create a hoteliers', () => {
            cy.fn_safeVisit('/hotels/cypress-a/hoteliers');
            cy.url().should('eq', Cypress.config().baseUrl + '/login')
            cy.get('div.title h2').should('contain.text','Hotelier Log in')
        })

        afterEach(function () {
            cy.fn_afterEachJira(this.currentTest)
        })
    })
})
