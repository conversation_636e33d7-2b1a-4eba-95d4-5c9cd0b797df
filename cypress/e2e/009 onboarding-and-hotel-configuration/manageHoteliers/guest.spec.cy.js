import accounts from '../../../fixtures/accounts';
import TestFilters from '../../../support/filterTests';

TestFilters(['P2Sanity'], () => {

    describe('On-Boarding: Guests can not create a hoteliers', () => {

        it('can log in', () => {
            cy.fn_login('guest', accounts.cypress_a.guest.email, accounts.cypress_a.guest.password, accounts.cypress_a.slug)
            cy.get('header h1').should('contain.text', 'Check availability')
        })

        it('can not create a hoteliers', () => {
            cy.fn_safeVisit('/hotels/cypress-a/hoteliers');
            cy.url().should('eq', Cypress.config().baseUrl + '/login')
            cy.get('.module__header').contains('Hotelier Log In')
        })

        afterEach(function () {
            cy.fn_afterEachJira(this.currentTest)
        })
    })
})
