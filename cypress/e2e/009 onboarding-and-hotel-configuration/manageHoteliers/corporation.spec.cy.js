import accounts from '../../../fixtures/accounts';
import TestFilters from '../../../support/filterTests';

TestFilters(['P2Sanity'], () => {

    describe('On-Boarding: Corporations can not create a hotel', () => {

        it('can log in', () => {
            cy.fn_login('corporation', accounts.cypress_a.corporation.email, accounts.cypress_a.corporation.password, accounts.cypress_a.slug)
            cy.get('header h1').should('contain.text', 'Check availability')
        })

        it('can not create a hotel', () => {
            cy.fn_safeVisit('/hotels/cypress-a/hoteliers');
            cy.url().should('eq', Cypress.config().baseUrl + '/login')
            cy.get('.module__header').should('contain.text', 'Hotelier Log In')
        })

        afterEach(function () {
            cy.fn_afterEachJira(this.currentTest)
        })
    })
})
