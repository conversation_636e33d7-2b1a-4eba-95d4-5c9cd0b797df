import accounts from '../../../fixtures/accounts';
import TestFilters from '../../../support/filterTests';

TestFilters(['P2Sanity'], () => {

    describe('Onboarding: Guests cannot manage Rates', () => {

        it('can log in', () => {
            cy.fn_login('guest', accounts.cypress_a.guest.email, accounts.cypress_a.guest.password, accounts.cypress_a.slug)
            cy.get('header h1').should('contain.text', 'Check availability')
        })

        it('cannot access rates', () => {
            cy.fn_safeVisit('/hotels/cypress-a/overview');
            cy.get('div.title h2').should('contain.text','Hotelier Log in')
        })

        it('cannot create a rate', () => {
            cy.fn_safeVisit('hotels/cardiff/v2/rates/create')
            cy.get('.module__header').should('contain.text','Page not found')
        })

        afterEach(function () {
            cy.fn_afterEach<PERSON>ira(this.currentTest)
        })
    })
})
