import accounts from '../../../fixtures/accounts';
import rates from '../../../fixtures/cypress_a/rates';
import TestFilters from '../../../support/filterTests';

TestFilters(['P2Sanity'], () => {

    describe('Onboarding: Hotelier Front Desk can manage Rates', () => {

        it('can log in', () => {
            cy.fn_login('hotelier', accounts.cypress_a.hotelier.front_desk.email, accounts.cypress_a.hotelier.front_desk.password)
            cy.get('.message.success').should('contain', 'Logged in')
        })

        it('can access rates', () => {
            // Set up listeners
            cy.intercept({method: 'GET', url: '/permissions'}).as('fetchPermissions');
            cy.intercept({method: 'POST', url: '/hotels/search'}).as('fetchHotels');
            cy.intercept({method: 'POST', url: '/roomTypes/search'}).as('fetchRoomTypes');
            cy.intercept({method: 'POST', url: '/rates'}).as('fetchRates');
            // Navigate to Rate Plans
            cy.contains('a', /Rate Plans/)
                .click({ force: true })
                .wait(['@fetchPermissions', '@fetchHotels', '@fetchRoomTypes', '@fetchRates'])
        })

        it('can see rates', () => {
            // Existing Rates should be visible
            cy.get('.overview-new').should('contain.text', 'Basic')
        })

        it('cannot create rates', () => {
            cy.get('.page-header').should('not.contain', 'Create New Rate')

            cy.fn_safeVisit('hotels/cardiff/v2/rates/create')
            cy.get('.module__header').should('contain.text','Page not found')
        })

        afterEach(function () {
            cy.fn_afterEachJira(this.currentTest)
        })
    })
})