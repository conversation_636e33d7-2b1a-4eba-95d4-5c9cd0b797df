import accounts from '../../../fixtures/accounts';
import rates from '../../../fixtures/cypress_a/rates';
import TestFilters from '../../../support/filterTests';

TestFilters(['P2Sanity'], () => {

    describe('Onboarding: Hotelier Manager can manage Rates', () => {

        it('can log in', () => {
            cy.fn_login('hotelier', accounts.cypress_a.hotelier.manager.email, accounts.cypress_a.hotelier.manager.password)
            cy.get('.message.success').should('contain', 'Logged in')
        })

        it('has one hotel', () => {
            cy.contains('a', `${accounts.cypress_a.title} Settings`).should('be.visible')
        })

        it('can create rates', () => {
            // Set up listeners
            cy.intercept({method: 'GET', url: '/permissions'}).as('fetchPermissions');
            cy.intercept({method: 'POST', url: '/hotels/search'}).as('fetchHotels');
            cy.intercept({method: 'POST', url: '/roomTypes/search'}).as('fetchRoomTypes');
            cy.intercept({method: 'POST', url: '/rates'}).as('fetchRates');
            cy.intercept({method: 'POST', url: '/rates/create'}).as('createRate');
            // Navigate to Rate Plans
            cy.contains('a', /Rate Plans/)
                .click({ force: true })
                .wait(['@fetchPermissions', '@fetchHotels', '@fetchRoomTypes', '@fetchRates'])
            // Create Rates
            rates.forEach(rate => {
                cy.contains('a', /Create New Rate/).click({force: true}).wait('@fetchRoomTypes')
                cy.get('.page-title').should('contain.text', 'Create Rate')
                cy.get('form').fillCreateRateForm(rate).submit().wait('@createRate')
                cy.url().should('contain', '/v2/rates/overview')
            })
        })

        it('can update a rate', () => {
            // Set up listeners
            cy.intercept({method: 'GET', url: '/permissions'}).as('fetchPermissions');
            cy.intercept({method: 'POST', url: '/hotels/search'}).as('fetchHotels');
            cy.intercept({method: 'POST', url: '/roomTypes/search'}).as('fetchRoomTypes');
            cy.intercept({method: 'POST', url: '/rates'}).as('fetchRates');
            cy.intercept({method: 'POST', url: '/rates/*'}).as('fetchRate');
            cy.intercept({method: 'POST', url: '/rates/create'}).as('createRate');
            // Navigate to Rate Plans
            cy.contains('a', /Rate Plans/)
                .click({ force: true })
                .wait(['@fetchPermissions', '@fetchHotels', '@fetchRoomTypes', '@fetchRates'])
                .wait(1000)
            // Select Rate to Edit
            let rate = rates[1]
            cy.contains('p', rate.name).first().parent().invoke('attr', 'id').then(id => {
                let RateId = id.split('-')[1];
                cy.get('#Edit-' + RateId ).click()
            })
            cy.get('.modal-header')
                .should('contain.text', rate.name)
                .wait(['@fetchRoomTypes', '@fetchRate'])
            // Update Rate
            cy.get('form').fillUpdateRateForm({
                name: rate.name + ' Updated',
                code: rate.code + '_upd',  // max: 20 chars
                description: rate.description + ' Updated',
                deposit: {
                    threshold: '30',
                    amount: '70',
                    strategy: 'Percentage',   // Percentage | Flat Rate
                    target: 'All days',  // First day | All days
                }
            }).submit()//.wait('@createRate')
            cy.url().should('contain', '/v2/rates/overview')
        })

        it('can delete a rate', () => {
            // Set up listeners
            cy.intercept({method: 'GET', url: '/permissions'}).as('fetchPermissions');
            cy.intercept({method: 'POST', url: '/hotels/search'}).as('fetchHotels');
            cy.intercept({method: 'POST', url: '/roomTypes/search'}).as('fetchRoomTypes');
            cy.intercept({method: 'POST', url: '/rates'}).as('fetchRates');
            // Navigate to Rate Plans
            cy.contains('a', /Rate Plans/).click({ force: true })
                .wait(['@fetchPermissions', '@fetchHotels', '@fetchRoomTypes', '@fetchRates'])
            // Delete Rates
            let rate = rates[0]
            // Find & Click Rate trash icon
            cy.contains('p', rate.name).first().parent().invoke('attr', 'id').then(id => {
                let RateId = id.split('-')[1];
                cy.get('#Delete-' + RateId ).click()
            })
            // Delete rate
            cy.intercept({method: 'DELETE', url: '/rates/*'}).as('deleteRate');
            cy.intercept({method: 'POST', url: '/rates'}).as('fetchRates');
            cy.get('.modal-content')
                .should('be.visible')
                .should('contain.text', 'Are you sure you want to delete this rate')
                .within(() => {
                    cy.get('button').contains('Delete').click()
                })
            cy.wait(['@deleteRate', '@fetchRates'])
        })

        afterEach(function () {
            cy.fn_afterEachJira(this.currentTest)
        })
    })
})
