import accounts from '../../../fixtures/accounts'
import TestFilters from '../../../support/filterTests';

TestFilters(['P2Sanity'], () => {

    describe('On-Boarding: Hotelier Front Desk cannot create a hotel', () => {

        it('can log in', () => {
            cy.fn_login('hotelier', accounts.cypress_a.hotelier.front_desk.email, accounts.cypress_a.hotelier.front_desk.password)
            cy.get('.message.success').should('contain', 'Logged in')
        })

        it('can not create a hotel', () => {
            cy.fn_safeVisit('/hotels');
            cy.get('.module__header').should('contain.text', 'Cypress Test Hotel A')
            cy.get('.module__header').should('not.contain', 'Cardiff Plaza')
            cy.contains('a', 'Create').should('be.visible').click()
            cy.get('.module__header').should('contain.text', 'Access Denied')
        })

        it('cannot update Hotel settings', () => {
            cy.safeVisit(`/hotels/${accounts.cypress_a.slug}/edit`);
            cy.get('.module__header').should('contain.text', 'Access Denied')
        })

        afterEach(function () {
            cy.fn_afterEachJira(this.currentTest)
        })
    })
})
