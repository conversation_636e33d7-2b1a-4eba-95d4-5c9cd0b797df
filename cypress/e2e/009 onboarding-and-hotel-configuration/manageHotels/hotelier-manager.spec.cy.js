import accounts from '../../../fixtures/accounts';
import TestFilters from '../../../support/filterTests';

TestFilters(['P2Sanity'], () => {

    describe('On-Boarding: Hotelier Manager', () => {

        it('can log in', () => {
            cy.fn_login('hotelier', accounts.cypress_a.hotelier.manager.email, accounts.cypress_a.hotelier.manager.password)
            cy.get('.message.success').should('contain', 'Logged in')
        })

        it('can not create a hotel', () => {
            cy.visit('/hotels');
            cy.get('.module__header').should('contain', 'Cypress Test Hotel A')
            cy.get('.module__header').should('not.contain', 'Cardiff Plaza')
            cy.contains('a', 'Create').should('be.visible').click()
            cy.get('.module__header').should('contain.text', 'Access Denied')
        })

        it('cannot see Slug and Email to edit', () => {
            cy.fn_login('hotelier', accounts.cypress_a.hotelier.manager.email, accounts.cypress_a.hotelier.manager.password)
            cy.get('.message.success').should('contain', 'Logged in')
            cy.contains('a', `${accounts.cypress_a.title} Settings`).click();
            cy.get('.col1 .form.box form input#slug').should('not.exist');
            cy.get('.col1 .form.box form input#email').should('not.exist');
        })

        afterEach(function () {
            cy.fn_afterEachJira(this.currentTest)
        })
    })
})
