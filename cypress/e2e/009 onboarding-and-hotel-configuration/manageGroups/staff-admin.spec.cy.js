import hotel from '../../../fixtures/hotel'
import hoteliers from '../../../fixtures/hoteliers'
import TestFilters from '../../../support/filterTests';

TestFilters(['P2Sanity'], () => {

    const group = {
        'name': 'Cypress Test Group',
        'description': 'Created for a cypress test'
    }

    const hotelier = hoteliers.filter(hotelier => {
        return hotelier.role === 'Manager';
    })[0];

    describe('Onboarding: Staff Admin can create a Group', () => {

        /**
         * We need to create a dedicated Hotel & Hotelier to create a Group from,
         * otherwise this test will only run successfully once
         */
        before(() => {
            // Login
            cy.fn_login('staff', Cypress.env('staff_email'), Cypress.env('staff_password'))
            // Create Hotel
            cy.contains('a', 'Create').should('be.visible').click()
            cy.get('form').fillCreateHotelForm(hotel).submit()
            // Create Hotelier
            cy.contains('a', /Staff/).click({force: true})
            cy.contains('a', /Create Staff/).click()
            cy.get('form').fillCreateHotelierForm({
                name: hotelier.name,
                role: hotelier.title,
                email: hotelier.email,
            }).submit()
        })

        it('can log in', () => {
            cy.fn_login('staff', Cypress.env('staff_email'), Cypress.env('staff_password'))
            cy.get('.message.success').should('contain', 'Logged in')
        })

        it('can create a group', () => {
            // Requests
            cy.intercept({method: 'POST', url: '/groups/search'}).as('fetchGroups');
            cy.intercept({method: 'POST', url: '/hotels/search',}).as('fetchHotels');
            cy.intercept({method: 'POST', url: '/groups'}).as('createGroup');
            cy.intercept({method: 'POST', url: '/authentication/token'}).as('getAuth');
            // Links are visible
            cy.contains('a', 'Hotels').should('be.visible').click()
            cy.contains('a', 'Groups').should('be.visible').click().wait('@fetchGroups')
            // Create a Group
            cy.contains('button', 'Create New Group').should('be.visible').click({force: true})
            cy.get('form').fillCreateGroupForm(group, hotel).submit().wait(['@createGroup', '@getAuth'])
            cy.get('div.table-react').should('contain', group.name)
        })

        afterEach(function () {
            cy.fn_afterEachJira(this.currentTest)
        })
    })
})
