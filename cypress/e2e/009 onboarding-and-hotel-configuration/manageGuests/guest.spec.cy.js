import accounts from '../../../fixtures/accounts';
import TestFilters from '../../../support/filterTests';

TestFilters(['P2Sanity'], () => {

    describe('On-Boarding: Guests cannot create guests', () => {

        it('can not create guests', () => {
            cy.fn_login('guest', accounts.cypress_a.guest.email, accounts.cypress_a.guest.password, accounts.cypress_a.slug)
            cy.get('header h1').should('contain.text', 'Check availability')

            cy.fn_safeVisit('/hotels/cypress-a/guests');
            cy.get('.module__header').should('contain.text','Access Denied')
        })

        afterEach(function () {
            cy.fn_afterEachJira(this.currentTest)
        })
    })
})
