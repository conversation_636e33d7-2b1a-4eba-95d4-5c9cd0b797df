import guests from '../../../fixtures/guests.js'
import accounts from '../../../fixtures/accounts';
import TestFilters from '../../../support/filterTests';

TestFilters(['P2Sanity'], () => {

    describe('On-Boarding: Staff Admins can create guests', () => {

        it('can log in', () => {
            cy.fn_login('staff', Cypress.env('staff_email'), Cypress.env('staff_password'))
            cy.get('.message.success').should('contain', 'Logged in')
        })

        it('can select a hotel', () => {
            cy.get('.col1 form').fillSearchForm({name: accounts.cypress_a.title}).submit()
            cy.get('.module__header').should('contain', accounts.cypress_a.title)
            cy.contains('a', accounts.cypress_a.title).click()
            cy.contains('a', `${accounts.cypress_a.title} Settings`).should('be.visible')
        })

        it('can create guests', () => {
            cy.contains('a', /Guests/).click({ force: true })
            guests.forEach(guest => {
                cy.contains('a', /Create guest/).click()
                cy.contains('.module__header', /Create guest/)
                cy.get('form').fillCreateGuestForm(guest).submit()
                cy.get('.message.success').should('contain', 'Guest account created')
                cy.get('.table__content')
                    .should('contain', guest.name)
                    .should('contain', guest.email)
            })
        })

        it('can search guests', () => {
            cy.fn_safeVisit('/hotels/cypress-a')
            cy.contains('a', 'Dashboard').should('be.visible')
            cy.contains('a', /Guests/).click({ force: true })

            // Search Guest by name
            cy.get('.col1 form').fillSearchForm({name: guests[0].name}).submit()
            cy.get('.table__content')
                .should('contain', guests[0].name)
                .should('contain', guests[0].email)
                .should('not.contain', guests[1].name)
                .should('not.contain', guests[1].email)

            // Search Guest by email
            cy.get('.col1 form').fillSearchForm({email: guests[0].email}).submit()
            cy.get('.table__content')
                .should('contain', guests[0].name)
                .should('contain', guests[0].email)
                .should('not.contain', guests[1].name)
                .should('not.contain', guests[1].email)

            // Search for a garbage string
            cy.get('.col1 form').fillSearchForm({email: 'q!w"e£r$t%y^u&i*o'}).submit()
            cy.get('.table__content')
                .should('contain', 'No data available in table')
        })

        it('can delete guests', () => {
            guests.forEach(guest => {
                cy.fn_safeVisit('/hotels/cypress-a/guests')
                cy.contains('td', guest.email).selectTrashCan().click()
                cy.get('.message.success').should('contain', 'Guest account deleted')
            })
        })

        afterEach(function () {
            cy.fn_afterEachJira(this.currentTest)
        })
    })
})
