import corporations from '../../../fixtures/corporations.js'
import accounts from '../../../fixtures/accounts';
import TestFilters from '../../../support/filterTests';

TestFilters(['P2Sanity'], () => {

    describe('On-Boarding: Staff Admins can create corporations', () => {

        it('can log in', () => {
            cy.fn_login('staff', Cypress.env('staff_email'), Cypress.env('staff_password'))
            cy.get('.message.success').should('contain', 'Logged in')
        })

        it('can select a hotel', () => {
            cy.get('.col1 form').fillSearchForm({name: accounts.cypress_a.title}).submit()
            cy.get('.module__header').should('contain', accounts.cypress_a.title)
            cy.contains('a', accounts.cypress_a.title).click()
            cy.contains('a', `${accounts.cypress_a.title} Settings`).should('be.visible')
        })

        it('can create corporations', () => {
            cy.contains('a', /Corporations/).click({ force: true })
            corporations.forEach(corporation => {
                cy.contains('a', /Create corporation/).click()
                cy.contains('.module__header', /Create corporation/)
                cy.get('form').fillCreateCorporationForm(corporation).submit()
                cy.get('.message.success').should('contain', 'Corporation account created')
                cy.get('.table__content')
                    .should('contain', corporation.name)
                    .should('contain', corporation.email)
            })
        })

        it('can search corporations', () => {
            cy.contains('a', /Corporations/).click({force:true})
            // Search Corporation by name
            cy.get('.col1 form').fillSearchForm({name: corporations[0].name}).submit()
            cy.get('.table__content')
                .should('contain', corporations[0].name)
                .should('contain', corporations[0].email)
                .should('not.contain', corporations[1].name)
                .should('not.contain', corporations[1].email)
            // Search Corporation by email
            cy.get('.col1 form').fillSearchForm({email: corporations[0].email}).submit()
            cy.get('.table__content')
                .should('contain', corporations[0].name)
                .should('contain', corporations[0].email)
                .should('not.contain', corporations[1].name)
                .should('not.contain', corporations[1].email)
            // Search for a garbage string
            cy.get('.col1 form').fillSearchForm({email: 'q!w"e£r$t%y^u&i*o'}).submit()
            cy.get('.table__content')
                .should('contain', 'No data available in table')
        })

        it('can delete corporations', () => {
            cy.fn_safeVisit('/hotels/cypress-a/corporations')
            corporations.forEach(corporation => {
                cy.contains('td', corporation.email).selectTrashCan().click()
                cy.get('.message.success').should('contain', 'Corporation account deleted')
            })
        })

        afterEach(function () {
            cy.fn_afterEachJira(this.currentTest)
        })
    })
})
