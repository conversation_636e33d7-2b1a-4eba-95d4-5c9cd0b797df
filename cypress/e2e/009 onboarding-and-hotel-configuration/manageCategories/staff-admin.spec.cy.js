import accounts from '../../../fixtures/accounts';
import categories from '../../../fixtures/categories';
import TestFilters from '../../../support/filterTests';

TestFilters(['P2Sanity'], () => {

    describe('Onboarding: Staff Admin can manage Categories', () => {

        it('can log in', () => {
            cy.fn_login('staff', Cypress.env('staff_email'), Cypress.env('staff_password'))
            cy.get('.message.success').should('contain', 'Logged in')
        })

        it('can select a hotel', () => {
            cy.get('.col1 form').fillSearchForm({name: accounts.cypress_a.title}).submit()
            cy.get('.module__header').should('contain', accounts.cypress_a.title)
            cy.contains('a', accounts.cypress_a.title).click()
            cy.contains('a', `${accounts.cypress_a.title} Settings`).should('be.visible')
        })

        it ('can create categories', () => {
            cy.contains('a', /Categories/).click()
            categories.forEach(category => {
                // Create Category
                cy.contains('a', /Create category/).click()
                cy.get('form').fillCreateCategoryForm(category).submit()
                cy.get('.message.success').should('contain', `Category ${category.title} created`)
                cy.get('.table__content')
                    .should('contain', category.title)
                    .should('contain', category.code)
            })
        });

        it ('can search categories', () => {
            // Navigate to Categories
            cy.contains('a', /Categories/).click()
            // Search Categories
            cy.get('.col1 form').fillSearchForm({
                code: categories[0].code
            }).submit()
            // Confirm filtered results
            cy.get('.table__content')
                .should('contain', categories[0].code)
                .should('not.contain', categories[1].code)
        });

        it('can delete a category', () => {
            let category = categories[0]
            // Delete a Category
            cy.contains('a', /Categories/).click({ force: true })
            cy.contains('td', category.code).selectTrashCan().click()
            cy.get('.message.success').should('contain', category.title + ' deleted')
        })

        afterEach(function () {
            cy.fn_afterEachJira(this.currentTest)
        })
    })
})
