import accounts from '../../../fixtures/accounts';
import TestFilters from '../../../support/filterTests';

TestFilters(['P2Sanity'], () => {

    describe('Onboarding: Corporations cannot manage Categories', () => {

        it('cannot access categories', () => {
            cy.fn_login('corporation', accounts.cypress_a.corporation.email, accounts.cypress_a.corporation.password, accounts.cypress_a.slug)
            cy.get('header h1').should('contain.text', 'Check availability')

            cy.fn_safeVisit('/hotels/cypress-a/categories');
            cy.get('div.title h2').should('contain.text','Hotelier Log in')
        })

        afterEach(function () {
            cy.fn_afterEachJira(this.currentTest)
        })
    })
})
