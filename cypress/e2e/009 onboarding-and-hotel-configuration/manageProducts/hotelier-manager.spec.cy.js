import accounts from '../../../fixtures/accounts';
import products from '../../../fixtures/cypress_a/products';
import TestFilters from '../../../support/filterTests';

TestFilters(['P2Sanity'], () => {

    describe('Onboarding: Hotelier Manager can manage Products', () => {

        it('can log in', () => {
            cy.fn_login('hotelier', accounts.cypress_a.hotelier.manager.email, accounts.cypress_a.hotelier.manager.password)
            cy.get('.message.success').should('contain', 'Logged in')
        })

        it('has one hotel', () => {
            cy.contains('a', `${accounts.cypress_a.title} Settings`).should('be.visible')
        })

        it ('can create products', () => {
            // Navigate to Products
            cy.contains('a', /Products/).click({ force: true })
            // Create Products
            products.forEach(product => {
                cy.contains('a', /Create product/).click()
                cy.get('form').fillCreateProductForm(product).submit()
                cy.get('.message.success').should('contain', `${product.name} created`)
                cy.get('.table__content')
                    .should('contain', product.name)
                    .should('contain', product.code)
            })
        })

        it ('can search products', () => {
            // Navigate to Products
            cy.contains('a', /Products/).click({ force: true })
            // Search Products
            cy.get('.col1 form').fillSearchForm({
                code: products[0].code
            }).submit()
            // Confirm filtered results
            cy.get('.table__content')
                .should('contain', products[0].code)
                .should('not.contain', products[1].code)
        })

        it('can delete a product', () => {
            let product = products[0]
            // Delete a Product
            cy.contains('a', /Products/).click({ force: true })
            cy.contains('td', product.code).selectTrashCan().click()
            // Code Error
            // cy.get('.message.success').should('contain', product.type + ' deleted')
            // Tmp
            cy.get('.product-index-table').should('not.contain', product.type)
        })

        afterEach(function () {
            cy.fn_afterEachJira(this.currentTest)
        })
    })
})
