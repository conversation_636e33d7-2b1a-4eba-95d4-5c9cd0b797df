import accounts from '../../../fixtures/accounts';
import TestFilters from '../../../support/filterTests';

TestFilters(['P2Sanity'], () => {

    describe('Onboarding: Guests cannot manage Products', () => {

        it('can log in', () => {
            cy.fn_login('guest', accounts.cypress_a.guest.email, accounts.cypress_a.guest.password, accounts.cypress_a.slug)
            cy.get('header h1').should('contain.text', 'Check availability')
        })

        it('cannot access products', () => {
            cy.fn_safeVisit('/hotels/cypress-a/products');
            cy.get('div.title h2').should('contain.text','Hotelier Log in')
        })

        afterEach(function () {
            cy.fn_afterEachJira(this.currentTest)
        })
    })
})
