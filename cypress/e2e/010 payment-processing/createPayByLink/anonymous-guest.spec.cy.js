import {payByLinkCode, judopay} from '../../../fixtures/payments'
import accounts from '../../../fixtures/accounts'
import TestFilters from '../../../support/filterTests';

TestFilters(['P2Sanity'], () => {

    describe('Payment Processing: Pay By Link as an anonymous Guest', () => {

        // Runs before each test in this describe() block.
        // To run it before every test, add beforeEach() to cypress/support/e2e.js.
        beforeEach(() => {
            // Clean up DB using cypress-mysql plugin:
            // 1. Make sure seeded payment request as incomplete
            cy.query('UPDATE payment_requests SET completed_at = null WHERE `code` = ?', [payByLinkCode])

            // 2. Remove guest-a's stored debit cards
            cy.query(`DELETE cc
                            FROM credit_cards cc 
                                JOIN hotels h ON cc.hotel_id = h.id
                                JOIN guests g ON cc.owner_id = g.id
                            WHERE cc.owner_type = 'Guest' 
                              AND h.slug = '${accounts.cypress_a.slug}'
                              AND g.email = '${accounts.cypress_a.guest.email}'`)
        })

        it('can pay using a link', () => {
            cy.fn_safeVisit(`/sp/${payByLinkCode}`)
            cy.contains('body', 'Request Payment').should('be.visible')

            // Using cypress-iframe plugin:
            // This will verify that the iframe is loaded to any page other than 'about:blank'
            cy.iframe('#card-capture-for-cardUuid')
                .find('#name').type(judopay.cards.successWithoutChallenge.cardholder)
            cy.iframe('#card-capture-for-cardUuid')
                .find('#number').type(judopay.cards.successWithoutChallenge.number)
            cy.iframe('#card-capture-for-cardUuid')
                .find('#expire-month').select(judopay.cards.successWithoutChallenge.expiry.month)
            cy.iframe('#card-capture-for-cardUuid')
                .find('#expire-year').select(judopay.cards.successWithoutChallenge.expiry.year)
            cy.iframe('#card-capture-for-cardUuid')
                .find('#ccv').type(judopay.cards.successWithoutChallenge.ccv)

            cy.iframe('#card-capture-for-cardUuid')
                .find('#card-capture-form').submit()

            cy.contains('div', 'was successful', { timeout: 40 * 1000}).should('be.visible')
        })

        afterEach(function () {
            cy.fn_afterEachJira(this.currentTest)
        })
    })
})
