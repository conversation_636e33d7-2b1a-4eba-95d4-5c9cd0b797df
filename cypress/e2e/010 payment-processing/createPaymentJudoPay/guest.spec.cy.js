import accounts from '../../../fixtures/accounts';
import TestFilters from '../../../support/filterTests';

TestFilters(['P2Sanity'], () => {

    describe('Payment Processing: Request a Payment By Link as a Guest', () => {

        it('can log in', () => {
            cy.fn_login('guest', accounts.cypress_a.guest.email, accounts.cypress_a.guest.password, accounts.cypress_a.slug)
            cy.contains('button', 'Login').should('not.exist')
            cy.contains('a.button.default', 'My Account').should('be.visible')
        })

        it('cannot create payment for a booking', () => {
            cy.fn_safeVisit(`/hotels/${accounts.cypress_a.slug}/bookings`)
            cy.contains('a', 'Log In').should('be.visible')
        })

        afterEach(function () {
            cy.fn_afterEachJira(this.currentTest)
        })
    })
})
