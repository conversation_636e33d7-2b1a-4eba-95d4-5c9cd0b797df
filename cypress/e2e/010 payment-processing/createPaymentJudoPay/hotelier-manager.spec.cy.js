import accounts from '../../../fixtures/accounts';
import {paymentMethods, cards, paymentAmount, judopay} from '../../../fixtures/payments';
import TestFilters from '../../../support/filterTests';

TestFilters(['P2Sanity'], () => {

    describe('Payment Processing: Create Payment For a Booking as Staff Admin', () => {
        // Runs before each test in this describe() block.
        // To run it before every test, add beforeEach() to cypress/support/e2e.js.
        beforeEach(() => {
            // Clean up DB using cypress-mysql plugin:
            // Remove guest-a's stored debit cards
            cy.query(`DELETE cc
                            FROM credit_cards cc 
                                JOIN hotels h ON cc.hotel_id = h.id
                                JOIN guests g ON cc.owner_id = g.id
                            WHERE cc.owner_type = 'Guest' 
                              AND h.slug = '${accounts.cypress_a.slug}'
                              AND g.email = '${accounts.cypress_a.guest.email}'`)
        })

        it('can log in', () => {
            cy.fn_login('hotelier', accounts.cypress_a.hotelier.manager.email, accounts.cypress_a.hotelier.manager.password)
            cy.get('.message.success').should('contain', 'Logged in')
        })

        it('can create payment for a booking', () => {
            // START Prerequisite: stored card

            // view a booking
            cy.fn_safeVisit(`/hotels/${accounts.cypress_a.slug}/bookings`)
            cy.get('[data-cy="booking_0"] > a').click()

            // click +Card
            cy.get('[data-cy="add-card"]').click()
            cy.contains('h1.title', 'Add or request a credit card').should('exist')

            // click 'Create credit card: Manually add a new card' item
            cy.get('[data-cy="create-credit-card"]').click({multiple: true, force: true})
            cy.contains('h2', 'Create credit card').should('exist')

            // choose 'Visa'
            cy.get('#type').select(cards.subtypes.visa)

            // fill out card details
            cy.get('#name').type(judopay.cards.successWithoutChallenge.cardholder)
            cy.get('#number').type(judopay.cards.successWithoutChallenge.number, {delay: 20})
            cy.get('#expiry').type(judopay.cards.successWithoutChallenge.expiry.MMYY)
            cy.get('#ccv').type(judopay.cards.successWithoutChallenge.ccv)

            cy.get('form').submit()

            cy.contains('strong', 'Credit Card created').should('be.visible')
            // END Prerequisite


            // view a booking
            cy.fn_safeVisit(`/hotels/${accounts.cypress_a.slug}/bookings`)
            cy.get('[data-cy="booking_0"] > a').click()

            // click Add Payment
            cy.get('[data-cy="add-payment"]').click()

            // click 'Process Payment: Process a real-time payment' item
            cy.get('[data-cy="process-real-time-payment"]').click({multiple: true, force: true})
            cy.contains('h1.title', 'Create Guest payment for').should('exist')

            // select 'Credit Card'
            cy.get('[data-cy="payment-methods"]').last().select(paymentMethods.card)

            // select one of the cards
            let cardQuery = `SELECT cc.uuid 
                                      FROM credit_cards cc
                                      JOIN guests g ON cc.owner_id = g.id
                                      JOIN hotels h ON cc.hotel_id = h.id
                                      WHERE cc.owner_type = 'Guest'
                                        AND g.email = '${accounts.cypress_a.guest.email}'
                                        AND h.slug = '${accounts.cypress_a.slug}'
                                      LIMIT 1`
            cy.query(cardQuery).then(cards => {
                cy.get('[data-cy="stored-card"]').last().select(cards[0].uuid)
            })

            // choose 'Visa'
            cy.get('[data-cy="card-subtypes"]').last().select(cards.subtypes.visa)

            // provide amount
            cy.get('[data-cy="payment-amount"]').last().type(paymentAmount)

            // provide optional notes
            cy.get('[data-cy="payment-notes"]').last().type('Test')

            cy.get('[data-cy="process-payment"]').last().click()

            // proceed with payment
            cy.get('[value="Proceed"]').click()

            // assert success
            cy.contains('processed successfully', {timeout: 15 * 1000})
        })

        afterEach(function () {
            cy.fn_afterEachJira(this.currentTest)
        })
    })
})
