import accounts from '../../../fixtures/accounts';
import TestFilters from '../../../support/filterTests';

TestFilters(['P2Sanity'], () => {

    describe('Payment Processing: Request a Payment By Link as Hotelier Manager', () => {

        it('can log in', () => {
            cy.fn_login('hotelier', accounts.cypress_a.hotelier.manager.email, accounts.cypress_a.hotelier.manager.password)
            cy.get('.message.success').should('contain', 'Logged in')
        })

        it('can request a Payment By Link', () => {
            // view a booking
            cy.fn_safeVisit(`/hotels/${accounts.cypress_a.slug}/bookings`)
            cy.get('[data-cy="booking_0"] > a').click()

            // click Add Payment
            cy.get('[data-cy="add-payment"]').click()

            // click 'Request a payment: Take a payment by link' item
            cy.get('[data-cy="request-pay-by-link"]').click({multiple: true, force: true})
            cy.contains('h2', 'Request a payment from').should('exist')

            // check 'Email to [...]'
            cy.get('[data-cy="email"]').check()

            // pay the minimal possible amount
            cy.get('[data-cy="amount"]').type('0.01')

            cy.get('form').submit()

            // assert success
            cy.contains('strong', 'Payment request sent').should('be.visible')
        })

        afterEach(function () {
            cy.fn_afterEachJira(this.currentTest)
        })
    })
})
