import accounts from '../../../fixtures/accounts';
import rates from '../../../fixtures/cypress_a/seeded_rates';
import TestFilters from '../../../support/filterTests';

TestFilters(['P2Sanity'], () => {

    describe('Group Management: Group Manager', () => {

        it('can log in to Group Portal', () => {
            cy.fn_login('group', accounts.cypress_a.group_manager.email, accounts.cypress_a.group_manager.password)
            cy.url()
                .should('contain', 'groups')
                .should('contain', accounts.cypress_a.group_manager.group_slug)
            cy.get('#downshift-1-toggle-button').should('contain.text', 'Group View')
        })

        it('can select Hotel rates', () => {
            cy.get('.router-nav').within(() => {
                // Click 'Rates'
                cy.contains('li .title', 'Rates').click()
                // Sub menu
                cy.contains('li .title', 'Rates').parent().within(() => {
                    cy.get('.content')
                        .should('be.visible')
                        .within(() => {
                            // Click `Plans`
                            cy.contains('a', 'Plans')
                                .should('be.visible')
                                .click()
                        })
                })
                cy.url()
                    .should('contain', 'rates')
                    .should('contain', 'overview')
            })
        })

        it('can see Hotel rates', () => {

            cy.intercept({method: 'POST', url: '/groups/search'}).as('rooms')
            cy.intercept({method: 'POST', url: '/hotels/search'}).as('hotels')
            cy.intercept({method: 'POST', url: '/roomTypes/search'}).as('rooms')
            cy.intercept({method: 'POST', url: '/rates'}).as('rates')

            cy.visit(`/groups/${accounts.cypress_a.group_manager.group_slug}/v2/rates/overview`)
                .wait(['@rooms','@hotels','@rooms','@rates'])

            cy.get('h1').should('contain', 'Rate Plans')

            // Loop Seeded Rates......
            rates.forEach(rate => {
                cy.contains('.header-row', rate.room_name)
                    .next('.oddRow')
                    .within(() => {
                        cy.get('.link.name p').should('contain', rate.name)
                    })
            })
        })

        afterEach(function () {
            cy.fn_afterEachJira(this.currentTest)
        })
    })
})
