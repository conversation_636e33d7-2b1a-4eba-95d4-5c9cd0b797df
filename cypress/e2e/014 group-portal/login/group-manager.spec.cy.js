import accounts from '../../../fixtures/accounts';
import TestFilters from '../../../support/filterTests';

TestFilters(['P2Sanity'], () => {

    describe('Group Management: Group Manager', () => {

        it('can log in to Group Portal', () => {
            cy.fn_login('group', accounts.cypress_a.group_manager.email, accounts.cypress_a.group_manager.password)
            cy.url()
                .should('contain', 'groups')
                .should('contain', accounts.cypress_a.group_manager.group_slug)
            cy.get('#downshift-1-toggle-button').should('contain.text', 'Group View')
        })

        afterEach(function () {
            cy.fn_afterEachJira(this.currentTest)
        })
    })
})
