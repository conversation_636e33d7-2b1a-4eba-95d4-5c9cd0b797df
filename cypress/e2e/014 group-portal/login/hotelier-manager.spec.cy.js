import accounts from '../../../fixtures/accounts';
import TestFilters from '../../../support/filterTests';

TestFilters(['P2Sanity'], () => {

    describe('Group Management: Hotelier Manager', () => {

        it('can log in to Group Portal', () => {
            cy.fn_login('group', accounts.cypress_a.hotelier.manager.email, accounts.cypress_a.hotelier.manager.password)
            cy.url().should('contain', 'login')
            cy.get('.text-danger.error-title')
                .should('be.visible')
                .and('contain.text', 'messages.token:unauthorized')
        })

        afterEach(function () {
            cy.fn_afterEachJira(this.currentTest)
        })
    })
})
