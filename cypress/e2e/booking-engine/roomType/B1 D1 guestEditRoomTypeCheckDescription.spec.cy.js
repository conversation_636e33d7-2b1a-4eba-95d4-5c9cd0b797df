import TestFilters from "../../../support/filterTests";
import accounts from "../../../fixtures/accounts";
import { updateRoomTypePage } from "../../../support/pageObjectModel/Pages/updateRoomTypePage";
import { pmsGeneral } from "../../../support/pageObjectModel/Components/pmsGeneral";
import { yourStayPage } from "../../../support/pageObjectModel/Pages/yourStay";

const originalDescription = 'Double Room'
const updatedDescription = 'updated test description'
const testRoomType = 'Double Room'
const hotelSlug = accounts.cypressHotel3.slug

TestFilters(['Booking Engine'], () => {

describe('Verify staff is able to edit a room description and view updated room description on the Your Stay page as guest', () => {

    before('Verify original description is displayed before edit', () => { 
        cy.request(`automation/tests/reseedHotel/${hotelSlug}`)
        cy.clearCookies();
        cy.clearLocalStorage();

        //verify original description is displayed before edit 
        yourStayPage.open(hotelSlug)
        cy.get(yourStayPage.selectors.roomBoxes).contains(testRoomType).parents(yourStayPage.selectors.roomBoxes).within(() => {
            cy.get(yourStayPage.selectors.roomTypeDescription).should('contain', originalDescription)
        })
        
    })

    it('Edit room type description, navigate to booking engine as guest and verify room type description is correct', () => {
        cy.fn_login('staff', Cypress.env('staff_email'), Cypress.env('staff_password'))
        cy.fn_safeVisit(`/hotels/${hotelSlug}/room-types/dbl/edit`)
        cy.get(updateRoomTypePage.selectors.roomDescriptionField).clear()
                                                                 .type(updatedDescription)
        updateRoomTypePage.submitForm()
        cy.get(pmsGeneral.selectors.messageSuccess).should('contain', `${testRoomType} updated`)
        cy.fn_logout()
        
        yourStayPage.open(hotelSlug)
        cy.get(yourStayPage.selectors.roomBoxes).contains(testRoomType).parents(yourStayPage.selectors.roomBoxes).within(() => {
            cy.get(yourStayPage.selectors.roomTypeDescription).should('contain', updatedDescription)
        })
    })

    afterEach(function () {
        cy.fn_afterEachJira(this.currentTest)
    })

})

})