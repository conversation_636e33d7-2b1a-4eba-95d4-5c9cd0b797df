import TestFilters from "../../../support/filterTests";
import accounts from "../../../fixtures/accounts";
import { pmsGeneral } from "../../../support/pageObjectModel/Components/pmsGeneral";
import { yourStayPage } from "../../../support/pageObjectModel/Pages/yourStay";
import { trashPage } from "../../../support/pageObjectModel/Pages/trash";

const testRoomType = { 'name': 'Single Room', 'code': 'sng', 'description': 'Single Room','letter': 'S', 'occupants': 1}
const hotelSlug = accounts.cypressHotel3.slug

TestFilters(['Booking Engine'], () => {

describe('Verify staff is able to restore a room type and room type is displayed on the Your Stay page as staff', () => {

    before('Access your stay page as staff and verify deleted room is not visible', () => { 
        cy.request(`automation/tests/reseedHotel/${hotelSlug}`)

        cy.fn_login('staff', Cypress.env('staff_email'), Cypress.env('staff_password'))

        //verify room type is not displayed on your stay page before restoration
        yourStayPage.open(hotelSlug)
        cy.get(yourStayPage.selectors.singleRoomBox).should('not.exist')

        //visit trash page and restore room
        trashPage.open(hotelSlug)
        trashPage.restoreRoomType(testRoomType.code)
        cy.get(pmsGeneral.selectors.messageSuccess).should('contain', `${testRoomType.name} restored`)
    })

    it('Navigate to booking engine as staff and verify room type is displayed on Your Stay page', () => {
        yourStayPage.open(hotelSlug)
        cy.get(yourStayPage.selectors.singleRoomBox).should('be.visible')
        cy.get(yourStayPage.selectors.roomBoxes).contains(testRoomType.name).parents(yourStayPage.selectors.roomBoxes).within(() => {
            cy.get(yourStayPage.selectors.checkAvailabilityButton).should('be.visible')
            cy.get(yourStayPage.selectors.roomTypeName).should('contain', testRoomType.name)
            cy.get(yourStayPage.selectors.roomTypeDescription).should('contain', testRoomType.description)
            cy.get(yourStayPage.selectors.roomTypeSleepsCounter).should('contain', `Sleeps ${testRoomType.occupants}`)
        })
    })

    afterEach(function () {
        cy.fn_afterEachJira(this.currentTest)
    })

    })
    
})