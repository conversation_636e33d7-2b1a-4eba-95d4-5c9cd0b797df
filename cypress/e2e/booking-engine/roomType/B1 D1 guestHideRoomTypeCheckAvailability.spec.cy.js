import TestFilters from "../../../support/filterTests";
import accounts from "../../../fixtures/accounts";
import { updateRoomTypePage } from "../../../support/pageObjectModel/Pages/updateRoomTypePage";
import { pmsGeneral } from "../../../support/pageObjectModel/Components/pmsGeneral";
import { yourStayPage } from "../../../support/pageObjectModel/Pages/yourStay";

const testRoomType = 'Double Room'
const hotelSlug = accounts.cypressHotel3.slug

TestFilters(['Booking Engine'], () => {

describe('Verify hidden roomtype is not displayed on the Your Stay page as guest', () => {

    before('Verify double room type is visible to guest before being hidden', () => {
        cy.request(`automation/tests/reseedHotel/${hotelSlug}`)
        cy.clearCookies();
        cy.clearLocalStorage();
        yourStayPage.open(hotelSlug)
        cy.get(yourStayPage.selectors.doubleRoomBox).should('be.visible')
    })

    it('Set room type to hidden, logout, access booking engine as guest and verify double room type is not displayed on Your Stay page', () => {
        cy.fn_login('staff', Cypress.env('staff_email'), Cypress.env('staff_password'))

        cy.fn_safeVisit(`/hotels/${hotelSlug}/room-types/dbl/edit`)
        updateRoomTypePage.setRoomTypeToHidden()
        updateRoomTypePage.submitForm()
        cy.get(pmsGeneral.selectors.messageSuccess).should('contain', `${testRoomType} updated`)
        cy.fn_logout()
        
        yourStayPage.open(hotelSlug)
        cy.get(yourStayPage.selectors.doubleRoomBox).should('not.exist')
    })

    afterEach(function () {
        cy.fn_afterEachJira(this.currentTest)
    })

    })
    
})