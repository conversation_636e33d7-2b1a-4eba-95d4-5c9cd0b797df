import TestFilters from "../../../support/filterTests";
import accounts from "../../../fixtures/accounts";
import { yourStayPage } from "../../../support/pageObjectModel/Pages/yourStay";
import { extrasPage } from "../../../support/pageObjectModel/Pages/extras";
import { guestDetailsPage } from "../../../support/pageObjectModel/Pages/guestDetails";
import { payPage } from "../../../support/pageObjectModel/Pages/pay";
import { thankYouPage } from "../../../support/pageObjectModel/Pages/thankYou";
import { bookingHubPage } from "../../../support/pageObjectModel/Pages/bookingHub";
import { gridPage } from "../../../support/pageObjectModel/Pages/grid";
import { calendarPage } from "../../../support/pageObjectModel/Pages/calendar";
import { pmsGeneral } from "../../../support/pageObjectModel/Components/pmsGeneral";

import { validJudoPayCards } from "../../../fixtures/cards/paymentDetailsJudopay";
import { guestDetails } from "../../../fixtures/guestDetails";
import { dateConverter } from "../../../support/functions/formatDate";
import { addDaysToDate } from "../../../support/functions/addDaysToDate";
import { bookingsPage } from "../../../support/pageObjectModel/Pages/bookings";
import { paymentIframe } from "../../../support/pageObjectModel/Components/paymentIFrame";

const card = validJudoPayCards.frictionlessSuccess
const date = cy.helpers.dateYMD();
const currentDateFormatted = dateConverter(date)
const nextDayDate = addDaysToDate(1)
const nextDayDateFormatted = dateConverter(nextDayDate)
const twoDaysAheadDate = addDaysToDate(2);
const twoDaysAheadDateFormatted = dateConverter(twoDaysAheadDate)
const threeDaysAheadDate = addDaysToDate(3)
const threeDaysAheadFormatted = dateConverter(threeDaysAheadDate)
const fourDaysAheadDate = addDaysToDate(4)

const hotelSlug = accounts.cypressHotel6.slug
const roomSelector = yourStayPage.selectors.twinRoomBox

TestFilters(['Booking Engine'], () => {

describe('Create booking as guest with multi-night reservation', () => {

    before('log in as staff and access booking engine as guest', () => {
  
    cy.request(`automation/tests/reseedHotel/${hotelSlug}`)
    cy.clearCookies();
    cy.clearLocalStorage();
    yourStayPage.open(hotelSlug)
  })

    it('Verify guest is able to make a booking with a 4 night reservation', () => {
    yourStayPage.clickSelectDates()
    yourStayPage.selectCurrentDate()
    yourStayPage.selectFourDaysAheadDate()

    //Verify number of nights is displayed correctly 
    cy.get(yourStayPage.selectors.calendarNumNights).should('contain', '4 nights')
    
    //Search for availability 
    yourStayPage.clickSearchButton()
    yourStayPage.clickOccupancySearchButton()

    //add twin room basic rate plan
    cy.get(roomSelector + ' ' + yourStayPage.selectors.addRatePlanButton).should('exist');
    cy.get(roomSelector + ' ' + yourStayPage.selectors.addRatePlanButton).eq(0).click();
    cy.get(pmsGeneral.selectors.spinner).should('not.be.visible');
    
    yourStayPage.clickContinueButton()
    extrasPage.clickContinueButton()

    //enter guest details
    guestDetailsPage.fillInGuestBasicInfo(guestDetails)
    guestDetailsPage.selectPostcodeLookupButton()
    guestDetailsPage.selectFirstAddressFromDropdown()
    guestDetailsPage.clickContinueButton()

    //Verify full deposit message is displayed 
    payPage.assertDepositPaymentMessage('800.00')
    paymentIframe.fillJudoPayDetails(card)

    thankYouPage.assertURL()
    thankYouPage.clickBillingBreakdownChevron()
    thankYouPage.assertGuestDetails(guestDetails)

    //post booking checks
    cy.get(thankYouPage.selectors.bookingReference).invoke('text').then(text => {
    const bookingRef = text
    cy.fn_login('staff', Cypress.env('staff_email'), Cypress.env('staff_password'))
    
    //verify booking is displayed on the dashboard
    cy.fn_safeVisit(`/hotels/${hotelSlug}?date=${date}`)
    cy.contains(bookingRef).should('be.visible')

    //Verify booking is displayed on the payment report 
    cy.fn_safeVisit(`/hotels/${hotelSlug}/reports/invoice/payment?date=${date}`)
    cy.contains(bookingRef).should('be.visible')
    
    //Assert calendar entry is displayed
    calendarPage.open(hotelSlug)
    calendarPage.assertBookingExists(bookingRef)

    //verify booking is visible on the bookings page
    bookingsPage.open(hotelSlug)
    cy.contains(bookingRef).should('be.visible')
    cy.contains(bookingRef).click()

    })

    //confirm booking is captured and accurate on booking hub page
    bookingHubPage.assertPaymentStatus('paid')
                  .assertPaymentAmount(800.00)
                  .assertBalanceAmount(0.00)
                  .assertGuestDetails(guestDetails)
                  .assertBookingDates(date, fourDaysAheadDate)
    bookingHubPage.clickExpandBookingButton()
    cy.get(bookingHubPage.selectors.reservationDetails).invoke('text')
     .then((text) => {
       const normalizedText = text.replace(/\s+/g, ' ').trim();
       expect(normalizedText).to.include('Base Rate : Twin Room')
                             .to.include('£800.00')
                             .to.include(`${currentDateFormatted} £200.00`)
                             .to.include(`${nextDayDateFormatted} £200.00`)
                             .to.include(`${twoDaysAheadDateFormatted} £200.00`)
                             .to.include(`${threeDaysAheadFormatted} £200.00`)
    })

    cy.get(bookingHubPage.selectors.bookedDate).should('contain', `${currentDateFormatted}` )
    bookingHubPage.assertCorrectCreditCardAttached(card)

    //Verify accurate rates grid availability 
  
    // gridPage.open(hotelSlug)
    // gridPage.assertNoAvailability(gridPage.cypressHotel6Selectors.twinRoomAvailabilityCurrentDay)
    //         .assertNoAvailability(gridPage.cypressHotel6Selectors.twinRoomAvailabilityNextDay)
    //         .assertNoAvailability(gridPage.cypressHotel6Selectors.twinRoomAvailabilityTwoDaysAhead)
    //         .assertNoAvailability(gridPage.cypressHotel6Selectors.twinRoomAvailabilityThreeDaysAhead)
    
    })

  afterEach(function () {
      cy.fn_afterEachJira(this.currentTest)
  })
    
    })
  })
    

    
