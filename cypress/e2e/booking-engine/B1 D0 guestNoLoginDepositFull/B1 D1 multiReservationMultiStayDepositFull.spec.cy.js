import TestFilters from '../../../support/filterTests';
import {guestDetailsPage} from '../../../support/pageObjectModel/Pages/guestDetails';
import {payPage} from '../../../support/pageObjectModel/Pages/pay';
import {bookingsPage} from '../../../support/pageObjectModel/Pages/bookings';
import {bookingHubPage} from '../../../support/pageObjectModel/Pages/bookingHub';
import { pmsGeneral } from '../../../support/pageObjectModel/Components/pmsGeneral';

import {guestDetails} from '../../../fixtures/guestDetails';
import {yourStayPage} from '../../../support/pageObjectModel/Pages/yourStay';
import accounts from '../../../fixtures/accounts';
import {extrasPage} from '../../../support/pageObjectModel/Pages/extras';
import {gridPage} from '../../../support/pageObjectModel/Pages/grid';
import {calendarPage} from '../../../support/pageObjectModel/Pages/calendar';
import { validJudoPayCards } from '../../../fixtures/cards/paymentDetailsJudopay';
import {thankYouPage} from '../../../support/pageObjectModel/Pages/thankYou';
import { dateConverter } from '../../../support/functions/formatDate';
import { addDaysToDate } from '../../../support/functions/addDaysToDate';
import { paymentIframe } from '../../../support/pageObjectModel/Components/paymentIFrame';

const card = validJudoPayCards.frictionlessSuccess
const firstRoomSelector = yourStayPage.selectors.twinRoomBox
const secondRoomSelector = yourStayPage.selectors.doubleRoomBox
const hotelSlug = accounts.cypressHotel6.slug
const date = cy.helpers.dateYMD();
const currentDateFormatted = dateConverter(date)
const nextDayDate = addDaysToDate(1)
const nextDayDateFormatted = dateConverter(nextDayDate)

TestFilters(['Booking Engine'], () => {

describe('Create multi-reservation, multi-stay booking as guest', () => {
    before('access as not logged in guest', () => {

        cy.request(`automation/tests/reseedHotel/${hotelSlug}`)

        cy.clearCookies();
        cy.clearLocalStorage();
    })

    it('Access booking engine as guest and create complex booking', () => {
        yourStayPage
            .open(hotelSlug)
            .clickSelectDates()
            .selectCurrentDate()
            .selectTwoDaysAheadDate()
            .clickSearchButton()
            yourStayPage.clickOccupancySearchButton()
            cy.get(firstRoomSelector + ' ' + yourStayPage.selectors.addRatePlanButton).should('exist');
            cy.get(firstRoomSelector + ' ' + yourStayPage.selectors.addRatePlanButton).eq(0).click();
            cy.get(pmsGeneral.selectors.spinner).should('not.be.visible');

            
            yourStayPage.clickSelectDates()
            .selectNextDayDate()
            .selectTwoDaysAheadDate()
            .clickSearchButton()
            yourStayPage.clickOccupancySearchButton()
            cy.get(secondRoomSelector + ' ' + yourStayPage.selectors.addRatePlanButton).should('exist');
            cy.get(secondRoomSelector + ' ' + yourStayPage.selectors.addRatePlanButton).eq(0).click();
            cy.get(pmsGeneral.selectors.spinner).should('not.be.visible');
            yourStayPage.basketComponent.clickContinueButton()

        extrasPage.basketComponent.clickContinueButton()
        guestDetailsPage
            .fillInGuestBasicInfo(guestDetails)
            .clickPostcodeLookupButton()
        guestDetailsPage
            .selectFirstAddressFromDropdown()
            .basketComponent.clickContinueButton()

        //Verify full deposit message is displayed 
        payPage.assertDepositPaymentMessage('475.00')
        paymentIframe.fillJudoPayDetails(card)

        thankYouPage.postBookingChecks(bookingRef => {
            cy.fn_login('staff', Cypress.env('staff_email'), Cypress.env('staff_password'))

            bookingsPage
                .open(hotelSlug)
                .assertBookingListed(bookingRef)

            bookingHubPage
                .open(bookingRef, hotelSlug)
                .clickExpandBookingButton()
        cy.get(bookingHubPage.selectors.reservationDetails).invoke('text').then((text) => {
            const normalizedText = text.replace(/\s+/g, ' ').trim();
        expect(normalizedText).to.include('Base Rate : Twin Room')
                              .to.include(`${currentDateFormatted} £200.00`)
                              .to.include(`${nextDayDateFormatted} £200.00`)
                              .to.include('Base Rate : Double Room')
                              .to.include(`${nextDayDateFormatted} £150.00`)
                })
            bookingHubPage.assertPaymentStatus('part-paid')
                .assertPaymentAmount(475.00)
                .assertBalanceAmount(75.00)
                .assertTotalAmount(550.00)
            bookingHubPage.assertGuestDetails(guestDetails)

            calendarPage
                .open(hotelSlug)
                .assertBookingExists(bookingRef)

            // gridPage
            //     .open(hotelSlug)
            //     .assertNoAvailability(gridPage.cypressHotel6Selectors.twinRoomAvailabilityCurrentDay)
            //     .assertNoAvailability(gridPage.cypressHotel6Selectors.twinRoomAvailabilityNextDay)
            //     .assertNoAvailability(gridPage.cypressHotel6Selectors.doubleRoomAvailabilityNextDay)
        })
    });

    afterEach(function () {
        cy.fn_afterEachJira(this.currentTest)
    })
    
})
})