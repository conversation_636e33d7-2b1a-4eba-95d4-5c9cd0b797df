import TestFilters from '../../../support/filterTests';
import {yourStayPage} from '../../../support/pageObjectModel/Pages/yourStay';
import accounts from '../../../fixtures/accounts';
import {editPackagePage} from '../../../support/pageObjectModel/Pages/packages/edit';

const hotelSlug = accounts.cypressHotel11.slug
const packageCode = 'bed-and-breakfast';
const packageTitle = 'Bed and Breakfast';
// Skipped due to FD-5060: Package is enabled when it should be disabled
// Re-enable this test after the issue is resolved and verified.

TestFilters(['Booking Engine'], () => {

describe.skip('Verify a disabled package plan cannot be booked as a staff', () => {

    it('Access booking engine as staff and verify a disabled package is absent', () => {
       
        cy.request(`automation/tests/reseedHotel/${hotelSlug}`)

        cy.fn_login('staff', Cypress.env('staff_email'), Cypress.env('staff_password'))

        //assert package is in disabled state 
        editPackagePage.open(hotelSlug, packageCode);
        cy.get(editPackagePage.selectors.statusSelect).find(editPackagePage.selectors.selectedDropdownOption).should('have.text', 'Disabled')

        //assert package is not displayed on your stay page
        yourStayPage
            .open(hotelSlug)
            .clickSelectDates()
            .selectNextDayDate()
            .selectTwoDaysAheadDate()
            .clickSearchButton()
            .clickOccupancySearchButton()
        yourStayPage.assertRatePlanNotListed(packageTitle)

    })

    afterEach(function () {
        cy.fn_afterEachJira(this.currentTest)
    })

})

})