
import TestFilters from '../../../support/filterTests';
import {yourStayPage} from '../../../support/pageObjectModel/Pages/yourStay';
import accounts from '../../../fixtures/accounts';
import {editPackagePage} from '../../../support/pageObjectModel/Pages/packages/edit';

// Skipped due to FD-3560: Package is enabled when it should be disabled
// Re-enable this test after the issue is resolved and verified.

TestFilters(['Booking Engine'], () => {

describe('Verify a hidden package plan cannot be booked as not logged in guest', () => {

    it.skip('Access booking engine as not logged in guest and verify a hidden package is absent', () => {
        const hotelSlug = accounts.cardiff.slug;
        const packageCode = 'Package123';
        const packageTitle = packageCode;

        cy.fn_login('staff', Cypress.env('staff_email'), Cypress.env('staff_password'))
        //assert package is in hidden state
        editPackagePage.open(hotelSlug, packageCode)
        cy.get(editPackagePage.selectors.visibilitySelect).find(editPackagePage.selectors.selectedDropdownOption).should('have.text', 'Hidden - Staff Only')
        cy.fn_logout()

        yourStayPage
            .open(hotelSlug)
            .clickSelectDates()
            .selectNextDayDate()
            .selectTwoDaysAheadDate()
            .clickSearchButton()
            .clickOccupancySearchButton();
        yourStayPage.assertRatePlanNotListed(packageTitle)
    });

    afterEach(function () {
        cy.fn_afterEachJira(this.currentTest)
    })
    
})
})