
import TestFilters from '../../../support/filterTests';
import { guestDetailsPage } from '../../../support/pageObjectModel/Pages/guestDetails';
import { payPage } from '../../../support/pageObjectModel/Pages/pay';
import { bookingsPage } from '../../../support/pageObjectModel/Pages/bookings';
import { bookingHubPage } from '../../../support/pageObjectModel/Pages/bookingHub';
import { editPackagePage } from '../../../support/pageObjectModel/Pages/packages/edit';

import { guestDetails } from '../../../fixtures/guestDetails';
import { yourStayPage } from '../../../support/pageObjectModel/Pages/yourStay';
import accounts from '../../../fixtures/accounts';
import { extrasPage } from '../../../support/pageObjectModel/Pages/extras';
import { calendarPage } from '../../../support/pageObjectModel/Pages/calendar';
import { validJudoPayCards } from '../../../fixtures/cards/paymentDetailsJudopay';
import { thankYouPage } from '../../../support/pageObjectModel/Pages/thankYou';
import { addDaysToDate } from '../../../support/functions/addDaysToDate';
import { paymentIframe } from '../../../support/pageObjectModel/Components/paymentIFrame';

const tomorrow = addDaysToDate(1);
let hotelSlug = accounts.cardiff.slug;
let packageTitle = 'Package123';
const packageCode = packageTitle;
const card = validJudoPayCards.frictionlessSuccess

// Skipped due to FD-3560: Package is enabled when it should be disabled
// Re-enable this test after the issue is resolved and verified.

TestFilters(['Booking Engine'], () => {

describe.skip('Book a package plan starting from the earliest available date as guest', () => {
    before('access as not logged in guest', () => {
        cy.clearCookies();
        cy.clearLocalStorage();
        //verify package has earliest date of tomorow 
        cy.fn_login('staff', Cypress.env('staff_email'), Cypress.env('staff_password'))

        //assert package is in disabled state 
        editPackagePage
            .open(hotelSlug, packageCode);
        cy.get(editPackagePage.selectors.earliestDateField).should('have.value', tomorrow)
        cy.fn_logout()
    }) 

    it('Access booking engine as guest and create package-based booking', () => {
        

        yourStayPage
            .open(hotelSlug)

            // negative test - package not visible today
            .clickSelectDates()
            .selectCurrentDate()
            .selectNextDayDate()
            .clickSearchButton()
            .clickOccupancySearchButton()
            // TODO? Wait until request finishes?
            yourStayPage.assertRatePlanNotListed(packageTitle)

            // positive test - package visible tomorrow
            .clickSelectDates()
            .selectNextDayDate()
            .selectTwoDaysAheadDate()
            .clickSearchButton()
            .clickOccupancySearchButton()
            // TODO? Wait until request finishes?
            yourStayPage.assertRatePlanListed(packageTitle)
            .addRatePlanByName(packageTitle)
            .basketComponent.clickContinueButton()

        extrasPage.basketComponent.clickContinueButton()

        guestDetailsPage
            .fillInGuestBasicInfo(guestDetails)
            .clickPostcodeLookupButton()
            .selectFirstAddressFromDropdown()
            .basketComponent.clickContinueButton()

        paymentIframe.fillJudoPayDetails(card)


        thankYouPage.postBookingChecks(bookingRef => {
            cy.fn_login('staff', Cypress.env('staff_email'), Cypress.env('staff_password'))

            bookingsPage
                .open()
                .assertBookingListed(bookingRef)

            bookingHubPage
                .open(bookingRef)
                .clickExpandBookingButton()
                .assertPaymentStatus('pending')
                .assertPaymentAmount(0.00)
                .assertBalanceAmount(75.00)
                .assertGuestDetails(guestDetails)
                .assertArrivalDate(tomorrow)

            calendarPage
                .open(hotelSlug)
                .assertBookingExists(bookingRef)
        })
    });

    afterEach(function () {
        cy.fn_afterEachJira(this.currentTest)
    })
    
})
})