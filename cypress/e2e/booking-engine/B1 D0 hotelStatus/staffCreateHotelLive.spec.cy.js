import TestFilters from "../../../support/filterTests";
import accounts from "../../../fixtures/accounts";
import { yourStayPage } from "../../../support/pageObjectModel/Pages/yourStay";
import { hotelSettingsPage } from "../../../support/pageObjectModel/Pages/hotelSettings";

const hotelSlug = accounts.cypressHotel3.slug

TestFilters(['Booking Engine'], () => {

describe('Verify staff is able to access the booking engine when hotel status is set to "Live"', () => {
    before('log in as staff and set hotel status to pending', () => {
    cy.request(`automation/tests/reseedHotel/${hotelSlug}`)

    cy.fn_login('staff', Cypress.env('staff_email'), Cypress.env('staff_password'))
    hotelSettingsPage.open(hotelSlug)
    hotelSettingsPage.assertStatusSetToLive()
    })

    it('verify staff is able to access booking engine', () => {
    yourStayPage.open(hotelSlug)
                .assertHeaderTitle()
    })

    afterEach(function () {
        cy.fn_afterEachJira(this.currentTest)
    })
    
})
})