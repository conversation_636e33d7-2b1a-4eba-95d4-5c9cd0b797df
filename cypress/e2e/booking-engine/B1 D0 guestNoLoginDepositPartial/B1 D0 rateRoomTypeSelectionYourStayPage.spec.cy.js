import TestFilters from '../../../support/filterTests';
import accounts from '../../../fixtures/accounts';
import { yourStayPage } from "../../../support/pageObjectModel/Pages/yourStay";
import { pmsGeneral } from "../../../support/pageObjectModel/Components/pmsGeneral";
import { extrasPage } from "../../../support/pageObjectModel/Pages/extras";
import { guestDetailsPage } from "../../../support/pageObjectModel/Pages/guestDetails";
import { guestDetails } from "../../../fixtures/guestDetails";
import { payPage } from "../../../support/pageObjectModel/Pages/pay";
import { validJudoPayCards } from '../../../fixtures/cards/paymentDetailsJudopay';
import { thankYouPage } from "../../../support/pageObjectModel/Pages/thankYou";
import { basketComponent } from "../../../support/pageObjectModel/Components/basket";
import { paymentIframe } from '../../../support/pageObjectModel/Components/paymentIFrame';

const hotelSlug = accounts.cypressHotel6.slug
const card = validJudoPayCards.frictionlessSuccess
const roomData =  [
    {
        roomType: 'Single Room',
        selector: yourStayPage.selectors.singleRoomBox,
        available: true,
        rateData: [
            {
                name: 'Base Rate',
                price: 100,
                available: true,
                addToBooking: true
            },
        ]
    },
    {
        roomType: 'Double Room',
        selector: yourStayPage.selectors.doubleRoomBox,
        available: true,
        rateData: [
            {
                name: 'Base Rate',
                price: 150,
                available: true,
                addToBooking: false
            },
        ]
    },
    {
        roomType: 'Family Room',
        selector: yourStayPage.selectors.familyRoomBox,
        available: true,
        rateData: [
            {
                name: 'Base Rate',
                price: 200,
                available: true,
                addToBooking: false
            },
        ]
    },
    {
        roomType: 'Luxury Room',
        selector: yourStayPage.selectors.luxuryRoomBox,
        available: true,
        rateData: [
            {
                name: 'Base Rate',
                price: 250,
                available: true,
                addToBooking: true
            },
        ]
    },
    {
        roomType: 'Twin Room',
        selector: yourStayPage.selectors.twinRoomBox,
        available: true,
        rateData: [
            {
                name: 'Base Rate',
                price: 200,
                available: true,
                addToBooking: false
            },
        ]
    }
];

const expectedConfirmationFinalPrice = '350.00';

TestFilters(['Booking Engine'], () => {

describe ('Assert that the correct rooms and rate plans appear as a guest on the booking engine', () => {
    it('Assert that the correct rooms and rate plans appear as a guest on the booking engine', {}, () => {

        let formatter = Intl.NumberFormat('en-GB', {style: 'currency', currency: 'GBP'});
        
        cy.request(`automation/tests/reseedHotel/${hotelSlug}`)

        cy.clearCookies();
        cy.clearLocalStorage();

        yourStayPage.open(hotelSlug)
        yourStayPage.clickSelectDates();
        yourStayPage.selectNextDayDate();
        yourStayPage.selectTwoDaysAheadDate();
        yourStayPage.clickSearchButton();
        cy.get(yourStayPage.selectors.selectNumAdultsInput).should('have.value', '2');
        cy.get(yourStayPage.selectors.selectNumChildrenInput).should('have.value', '0');
        yourStayPage.clickAdultSubtractCounter();
        yourStayPage.clickOccupancySearchButton();

        // Ensure that all rooms/rates exist as expected
        roomData.forEach((roomDatum) => {

            if (!roomDatum.available) {
                cy.get(roomDatum.selector).should('not.exist');
                cy.get(roomDatum.selector).should('not.be.visible');
                return;
            }

            cy.get(roomDatum.selector).should('exist');
            cy.get(roomDatum.selector).should('be.visible');

            roomDatum.rateData.forEach((rateDatum) => {

                if (!rateDatum.available) {
                    cy.get(roomDatum.selector).should('not.contain', rateDatum.name);
                    return;
                }

                cy.get(roomDatum.selector).should('contain', rateDatum.name);
                cy.get(roomDatum.selector).should('contain', rateDatum.price);
            });

        });

        let expectedTotalPrice = 0;

        // Add the rooms/rates that we want to the booking
        roomData.forEach((roomDatum) => {

            if (!roomDatum.available) {
                return;
            }

            let rateIndex = 0;

            roomDatum.rateData.forEach((rateDatum) => {

                if (!rateDatum.available) {
                    return;
                }

                if (!rateDatum.addToBooking) {
                    rateIndex++;
                    return;
                }

                // Add the rate to the booking
                cy.get(roomDatum.selector + ' ' + yourStayPage.selectors.addRatePlanButton).eq(rateIndex).should('exist');
                cy.get(roomDatum.selector + ' ' + yourStayPage.selectors.addRatePlanButton).eq(rateIndex).click();
                cy.get(pmsGeneral.selectors.spinner, {timeout:  20000}).should('not.be.visible');
                cy.get(pmsGeneral.selectors.toastContainer).should('contain', '1 Reservation added to Cart');
                rateIndex++;
                expectedTotalPrice += rateDatum.price;

                // Ensure that the correct room and rate has been added
                cy.get(basketComponent.selectors.reservationBreakdownItem)
                    .contains(roomDatum.roomType)
                    .parent()
                    .contains(rateDatum.name).should('exist');
            });
        });

        yourStayPage.assertBasketTotal(formatter.format(expectedTotalPrice).slice(1));

        // Create the booking
        yourStayPage.clickContinueButton();

        extrasPage.assertURL(hotelSlug)
        extrasPage.clickContinueButton();

        cy.get(guestDetailsPage.selectors.yourDetailsBox).should('exist');
        cy.get(guestDetailsPage.selectors.guestDetailsForm)
            .fillBookingEngineGuestDetailsForm(guestDetails);
        guestDetailsPage.clickContinueButton();

        payPage.assertURL()
        paymentIframe.fillJudoPayDetails(card)

        thankYouPage.assertURL()
        thankYouPage.assertBookingConfirmed();
        thankYouPage.assertPriceIsCorrect(expectedConfirmationFinalPrice);
    });

    afterEach(function () {
        cy.fn_afterEachJira(this.currentTest)
    })
})
})