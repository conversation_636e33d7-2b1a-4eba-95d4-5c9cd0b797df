import TestFilters from "../../../support/filterTests";
import { yourStayPage } from "../../../support/pageObjectModel/Pages/yourStay";
import { extrasPage } from "../../../support/pageObjectModel/Pages/extras";
import { basketComponent } from "../../../support/pageObjectModel/Components/basket";
import { guestDetails } from "../../../fixtures/guestDetails";
import { guestDetailsPage } from "../../../support/pageObjectModel/Pages/guestDetails";
import { validJudoPayCards } from "../../../fixtures/cards/paymentDetailsJudopay";
import { pmsGeneral } from "../../../support/pageObjectModel/Components/pmsGeneral";
import { paymentIframe } from "../../../support/pageObjectModel/Components/paymentIFrame";
import { payPage } from "../../../support/pageObjectModel/Pages/pay";
import { thankYouPage } from "../../../support/pageObjectModel/Pages/thankYou";
import { bookingHubPage } from "../../../support/pageObjectModel/Pages/bookingHub";
import accounts from "../../../fixtures/accounts";
import { products } from "../../../fixtures/cypress_6/products";

const hotelSlug = accounts.cypressHotel6.slug
const roomSelector = yourStayPage.selectors.twinRoomBox;
const expectedRoomCost = 200;
const card = validJudoPayCards.frictionlessSuccess


TestFilters(['Booking Engine'], () => {

describe ('Add/Remove extras, ensuring the price changes appropriately and create booking', () => {

    let formatter = Intl.NumberFormat('en-GB', {style: 'currency', currency: 'GBP'});

    it('Add/Remove extras, ensuring the price changes appropriately and create booking', {}, () => {

        cy.request(`automation/tests/reseedHotel/${hotelSlug}`)

        cy.clearCookies();
        cy.clearLocalStorage();

        yourStayPage.open(hotelSlug)
        yourStayPage.clickSelectDates();
        yourStayPage.selectNextDayDate();
        yourStayPage.selectTwoDaysAheadDate();
        yourStayPage.clickSearchButton();
        yourStayPage.clickOccupancySearchButton();

        cy.get(roomSelector + ' ' + yourStayPage.selectors.addRatePlanButton).should('exist');
        cy.get(roomSelector + ' ' + yourStayPage.selectors.addRatePlanButton).eq(0).click();
        cy.get(pmsGeneral.selectors.spinner).should('not.be.visible')
        yourStayPage.clickContinueButton();

        // Ensure that the room cost is what we would expect
        let finalExpectedPrice = expectedRoomCost;
        cy.get(basketComponent.selectors.basketTotal).should('exist');
        cy.get(basketComponent.selectors.basketTotal).should('contain', formatter.format(finalExpectedPrice));

        // Ensure that all the products that we are going to test exist and have the correct price
        let itemsAdded = 0;

        products.forEach((product,i) => {

            if (product.quantity === 0) {
                return;
            }

            // Ensure that the given product exists on the page and has the correct price
            let productSelector = extrasPage.getProductSelector(product.productIndex);
            let priceSelector = productSelector + ' ' + extrasPage.selectors.productPrice;
            cy.get(productSelector).should('exist');
            cy.get(productSelector).should('contain', product.productName);
            cy.get(priceSelector).should('contain', '£' + product.expectedProductPrice.toString());

            // Attempt to add the product and ensure that it was added
            extrasPage.addProductToBasket(product.productIndex, product.quantity, product.reservationIndex);

            finalExpectedPrice += product.expectedProductPrice * product.quantity;
            product.basketIndex = itemsAdded++;
            cy.get(pmsGeneral.selectors.spinner).should('not.be.visible');
            cy.get(pmsGeneral.selectors.toastContainer).should('contain', product.quantity.toString() + ' ' + product.productName + ' added to Cart');
        });

        // Sum the current price up and ensure that it matches what we expect
        cy.get(basketComponent.selectors.basketTotal).should('exist');
        cy.get(basketComponent.selectors.basketTotal).should('contain', formatter.format(finalExpectedPrice));

        // Remove any products that we need to
        products.forEach((product) => {
            if (!product.removeAfterAdding) {
                return;
            }

            basketComponent.removeProductFromBasket(product.basketIndex);
            finalExpectedPrice -= (product.quantity * product.expectedProductPrice);

        });

        // Sum the price up again with the products removed
        cy.get(basketComponent.selectors.basketTotal).should('exist');
        cy.get(basketComponent.selectors.basketTotal).should('contain', formatter.format(finalExpectedPrice));

        extrasPage.clickContinueButton();

        cy.get(guestDetailsPage.selectors.yourDetailsBox).should('exist');
        cy.get(guestDetailsPage.selectors.guestDetailsForm)
            .fillBookingEngineGuestDetailsForm(guestDetails);

        guestDetailsPage.clickContinueButton();

        payPage.assertURL()
        payPage.assertDepositPaymentMessage('220.97')
        paymentIframe.fillJudoPayDetails(card)

        thankYouPage.assertURL()
        thankYouPage.assertPriceIsCorrect('220.97')

        // Ensure that the booking hub page contains the correct information
        cy.get(thankYouPage.selectors.bookingReference).then(($element) => {

            // Navigate to the booking page and ensure that the correct values ae present.
            let bookingReference = $element.text();

            cy.fn_login('staff', Cypress.env('staff_email'), Cypress.env('staff_password'));

            bookingHubPage.open(bookingReference, hotelSlug)
            cy.contains(bookingReference).should('be.visible');

            // Ensure that the total matches what we would expect
            bookingHubPage.assertTotalAmount(formatter.format(finalExpectedPrice).slice(1));
            bookingHubPage.assertBalanceAmount(0.00)
                          .assertPaymentAmount(220.97)
            bookingHubPage.assertGuestDetails(guestDetails);

            // Ensure that the products that we would expect are present, and those that we removed are not.
            products.forEach((product) => {
                if (!product.removeAfterAdding) {
                    // Product should exist on the booking
                    cy.get(bookingHubPage.selectors.reservationDetails).should('contain', product.productName);
                } else {
                    // Product should not exist on the booking
                    cy.get(bookingHubPage.selectors.reservationDetails).should('not.contain', product.productName);
                }
            });
        });
    });

    afterEach(function () {
        cy.fn_afterEachJira(this.currentTest)
    })
    
})
})