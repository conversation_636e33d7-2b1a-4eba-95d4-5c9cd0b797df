import TestFilters from '../../../support/filterTests';
import {guestDetailsPage} from '../../../support/pageObjectModel/Pages/guestDetails';
import {payPage} from '../../../support/pageObjectModel/Pages/pay';
import {bookingsPage} from '../../../support/pageObjectModel/Pages/bookings';
import {bookingHubPage} from '../../../support/pageObjectModel/Pages/bookingHub';

import {guestDetails} from '../../../fixtures/guestDetails';
import {yourStayPage} from '../../../support/pageObjectModel/Pages/yourStay';
import accounts from '../../../fixtures/accounts';
import {extrasPage} from '../../../support/pageObjectModel/Pages/extras';
import {gridPage} from '../../../support/pageObjectModel/Pages/grid';
import {calendarPage} from '../../../support/pageObjectModel/Pages/calendar';
import { validJudoPayCards } from '../../../fixtures/cards/paymentDetailsJudopay';
import {thankYouPage} from '../../../support/pageObjectModel/Pages/thankYou';
import { pmsGeneral } from '../../../support/pageObjectModel/Components/pmsGeneral';
import { paymentIframe } from '../../../support/pageObjectModel/Components/paymentIFrame';

const card = validJudoPayCards.frictionlessSuccess
const firstRoomSelector = yourStayPage.selectors.luxuryRoomBox
const secondRoomSelector = yourStayPage.selectors.doubleRoomBox
const hotelSlug = accounts.cypressHotel6.slug
const date = cy.helpers.dateYMD();

TestFilters(['Booking Engine'], () => {

describe('Create multi-reservation, multi-stay booking as guest', () => {
    before('access as not logged in guest', () => {
      
        cy.request(`automation/tests/reseedHotel/${hotelSlug}`)
        cy.clearCookies();
        cy.clearLocalStorage();
    })

    it('Access booking engine as guest and create complex booking', () => {
        yourStayPage
            .open(hotelSlug)
            .clickSelectDates()
            .selectCurrentDate()
            .selectTwoDaysAheadDate()
            .clickSearchButton()
            .clickOccupancySearchButton()
            cy.get(firstRoomSelector + ' ' + yourStayPage.selectors.addRatePlanButton).should('exist');
            cy.get(firstRoomSelector + ' ' + yourStayPage.selectors.addRatePlanButton).eq(0).click();
            cy.get(pmsGeneral.selectors.spinner).should('not.be.visible');

        yourStayPage.clickSelectDates()
                    .selectCurrentDate()
                    .selectNextDayDate()
                    .clickSearchButton()
                    .clickOccupancySearchButton()
            cy.get(secondRoomSelector + ' ' + yourStayPage.selectors.addRatePlanButton).should('exist');
            cy.get(secondRoomSelector + ' ' + yourStayPage.selectors.addRatePlanButton).eq(0).click();
            cy.get(pmsGeneral.selectors.spinner).should('not.be.visible');

        yourStayPage.basketComponent.clickContinueButton()

        extrasPage.basketComponent.clickContinueButton()

        guestDetailsPage
            .fillInGuestBasicInfo(guestDetails)
            .clickEnterAddressManually()
            .fillInGuestAddressFields(guestDetails)
            .basketComponent.clickContinueButton()

        payPage
            .assertDepositPaymentMessage('175.00')
        paymentIframe.fillJudoPayDetails(card)

        thankYouPage.postBookingChecks(bookingRef => {
            cy.fn_login('staff', Cypress.env('staff_email'), Cypress.env('staff_password'))

            bookingsPage
                .open(hotelSlug)
                .assertBookingListed(bookingRef)

            bookingHubPage
                .open(bookingRef, hotelSlug)
                .clickExpandBookingButton()
                .assertPaymentStatus('part-paid')
                .assertPaymentAmount(175.00)
                .assertBalanceAmount(475.00)
                .assertGuestDetails(guestDetails)

            calendarPage
                .open(hotelSlug, date)
                .assertBookingExists(bookingRef)

            gridPage
                .open(hotelSlug)
                .assertNoAvailability(gridPage.cypressHotel6Selectors.luxuryRoomAvailabilityCurrentDay)
                .assertNoAvailability(gridPage.cypressHotel6Selectors.luxuryRoomAvailabilityNextDay)
                .assertNoAvailability(gridPage.cypressHotel6Selectors.doubleRoomAvailabilityCurrentDay)
        })
    });

    afterEach(function () {
        cy.fn_afterEachJira(this.currentTest)
    })
    
})
})