import TestFilters from "../../../support/filterTests";
import { pmsGeneral } from "../../../support/pageObjectModel/Components/pmsGeneral";
import { yourStayPage } from "../../../support/pageObjectModel/Pages/yourStay";
import { basketComponent } from "../../../support/pageObjectModel/Components/basket";
import { createVoucherPage } from "../../../support/pageObjectModel/Pages/createVoucherPage";
import accounts from "../../../fixtures/accounts";

const voucherCode = 'B1D1GAVCYS' + Date.now().toString().slice(-8);
const voucherName = 'B1D1-guestAddVoucherCheckYourStay';
const hotelSlug = accounts.cypressHotel4.slug

const voucherAmount = 1000;
const expectedFinalValue = '£240';
const roomSelector = yourStayPage.selectors.luxuryRoomBox;

TestFilters(['Booking Engine'], () => {

describe ('Create a voucher in the PMS and then attempt to use it on the booking engine', () => {

    it('Create a voucher and then attempt to use it', () => {

        cy.request(`automation/tests/reseedHotel/${hotelSlug}`)
        cy.fn_login('staff', Cypress.env('staff_email'), Cypress.env('staff_password'));

        createVoucherPage.open(hotelSlug)

        cy.get(createVoucherPage.selectors.form).fillCreateVoucherForm({
            description: voucherName,
            code: voucherCode,
            amount: voucherAmount,
            strategy: 'byAmount',
            room_type_id: 'all'
        }).submit();

        cy.get(pmsGeneral.selectors.messageSuccess).should('exist').should('contain', voucherName + ' Voucher created');
        cy.fn_logout()

        // Attempt to create a booking and use the voucher
        cy.clearCookies();
        cy.clearLocalStorage();

        yourStayPage.open(hotelSlug)
        yourStayPage.clickSelectDates();
        yourStayPage.selectNextDayDate();
        yourStayPage.selectTwoDaysAheadDate();
        yourStayPage.clickSearchButton();
        yourStayPage.clickOccupancySearchButton();

        // Add the voucher
        yourStayPage.addVoucher(voucherCode).assertVoucherIsAdded();

        // Add double room base rate
        cy.get(roomSelector + ' ' + yourStayPage.selectors.addRatePlanButton).should('exist');
        cy.get(roomSelector + ' ' + yourStayPage.selectors.addRatePlanButton).eq(0).click();
        cy.get(pmsGeneral.selectors.spinner).should('not.be.visible');

        cy.get(basketComponent.selectors.basketTotal).should('contain', expectedFinalValue);
    });

    afterEach(function () {
        cy.fn_afterEachJira(this.currentTest)
    })

})

})