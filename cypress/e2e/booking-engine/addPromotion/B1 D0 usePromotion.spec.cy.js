import TestFilters from '../../../support/filterTests'
import {guestDetailsPage} from '../../../support/pageObjectModel/Pages/guestDetails'
import {guestDetails} from '../../../fixtures/guestDetails'

import accounts from '../../../fixtures/accounts'
import {yourStayPage} from '../../../support/pageObjectModel/Pages/yourStay'
import { validJudoPayCards } from '../../../fixtures/cards/paymentDetailsJudopay'
import {thankYouPage} from '../../../support/pageObjectModel/Pages/thankYou';
import {basketComponent} from "../../../support/pageObjectModel/Components/basket";
import { paymentIframe } from '../../../support/pageObjectModel/Components/paymentIFrame'

const hotelSlug = accounts.cypressHotel5.slug
const card = validJudoPayCards.frictionlessSuccess

TestFilters(['Booking Engine'], () => {

describe('Use promotion as guest', () => {
    before('log in as staff', () => {
        cy.request(`automation/tests/reseedHotel/${hotelSlug}`)
        cy.clearCookies()
        cy.clearLocalStorage()
    })

    it('Access booking engine as guest and verify user can use promotion code', () => {

        const promotionCode = 'Test1'
        const rateName = 'PromoRate';
        const ratePrice = 39.99

        yourStayPage
            .open(hotelSlug)
            .addPromotion(promotionCode)
            .assertPromotionIsAdded()
        cy.reload()
        yourStayPage.clickSelectDates()
            .selectCurrentDate()
            .selectNextDayDate()
            .clickSearchButton()
            .clickOccupancySearchButton()
        yourStayPage.assertIsVisible(rateName)
            .assertIsVisible(ratePrice)
            .addRatePlanByName(rateName)
            .basketComponent.assertTotal(ratePrice)

        basketComponent
            .clickContinueButton() // going to extras page
            .clickContinueButton() // skipping extras page

        guestDetailsPage
            .fillInGuestBasicInfo(guestDetails)
            .clickEnterAddressManually()
            .fillInGuestAddressFields(guestDetails)

        basketComponent.clickContinueButton()

        paymentIframe.fillJudoPayDetails(card)

        thankYouPage
            .assertBookingConfirmed()
            .assertPriceIsCorrect(ratePrice)
    })

    afterEach(function () {
        cy.fn_afterEachJira(this.currentTest)
    })
})
})