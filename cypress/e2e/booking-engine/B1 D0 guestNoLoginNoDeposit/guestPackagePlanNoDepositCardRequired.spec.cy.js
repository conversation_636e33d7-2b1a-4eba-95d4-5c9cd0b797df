import TestFilters from '../../../support/filterTests';
import {guestDetailsPage} from '../../../support/pageObjectModel/Pages/guestDetails';
import {payPage} from '../../../support/pageObjectModel/Pages/pay';
import {bookingsPage} from '../../../support/pageObjectModel/Pages/bookings';
import {bookingHubPage} from '../../../support/pageObjectModel/Pages/bookingHub';

import {guestDetails} from '../../../fixtures/guestDetails';
import {yourStayPage} from '../../../support/pageObjectModel/Pages/yourStay';
import accounts from '../../../fixtures/accounts';
import {extrasPage} from '../../../support/pageObjectModel/Pages/extras';
import {calendarPage} from '../../../support/pageObjectModel/Pages/calendar';
import { validJudoPayCards } from '../../../fixtures/cards/paymentDetailsJudopay';
import {thankYouPage} from '../../../support/pageObjectModel/Pages/thankYou';
import {addDaysToDate} from '../../../support/functions/addDaysToDate';
import {gridPage} from '../../../support/pageObjectModel/Pages/grid';
import { paymentIframe } from '../../../support/pageObjectModel/Components/paymentIFrame';

const date = cy.helpers.dateYMD()
const tomorrow = addDaysToDate(1);
const hotelSlug = accounts.cypressHotel11.slug
const card = validJudoPayCards.frictionlessSuccess

TestFilters(['Booking Engine'], () => {

describe('Book a package plan as not logged in guest', () => {
    before('access as not logged in guest', () => {
        cy.request(`automation/tests/reseedHotel/${hotelSlug}`)
    })

    it('Access booking engine as not logged in guest and create package-based booking', () => {
        let packageTitle = 'Bed and breakfast';

        yourStayPage
            .open(hotelSlug)
            .clickSelectDates()
            .selectNextDayDate()
            .selectTwoDaysAheadDate()
            .clickSearchButton()
            .clickOccupancySearchButton()
            // TODO? Wait until request finishes?
        yourStayPage
            .assertRatePlanListed(packageTitle)
            .addRatePlanByName(packageTitle)
            .basketComponent.clickContinueButton()

        extrasPage.basketComponent.clickContinueButton()

        guestDetailsPage
            .fillInGuestBasicInfo(guestDetails)
            .clickPostcodeLookupButton()
            .clickPostcodeLookupButton()
            .selectFirstAddressFromDropdown()
            guestDetailsPage.basketComponent.clickContinueButton()

        payPage
            .assertNoDepositMessage()
        paymentIframe.fillJudoPayDetails(card)

        thankYouPage.postBookingChecks(bookingRef => {
            cy.fn_login('staff', Cypress.env('staff_email'), Cypress.env('staff_password'))

            bookingsPage
                .open(hotelSlug)
                .assertBookingListed(bookingRef)
                cy.contains(bookingRef).click()
            
                bookingHubPage.assertPaymentStatus('pending')
                .assertPaymentAmount(0.00)
                .assertBalanceAmount(75.00)
                .assertGuestDetails(guestDetails)
                .assertArrivalDate(tomorrow)
                .assertCorrectCreditCardAttached(card)

            calendarPage
                .open(hotelSlug, date)
                .assertBookingExists(bookingRef)

            // gridPage
            //     .open(hotelSlug)
            //     .assertNoAvailability(gridPage.cypressHotel11Selectors.doubleRoomAvailabilityNextDay)
        })
    })

    afterEach(function () {
        cy.fn_afterEachJira(this.currentTest)
    })
    
})
})