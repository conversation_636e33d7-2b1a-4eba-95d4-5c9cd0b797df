import TestFilters from '../../../support/filterTests';
import {yourStayPage} from '../../../support/pageObjectModel/Pages/yourStay';
import {cancellationPolicyModal} from '../../../support/pageObjectModel/Components/cancellationPolicyModal';
import accounts from '../../../fixtures/accounts';
import {footerComponent} from '../../../support/pageObjectModel/Components/footer';

const hotelSlug = accounts.cypressHotel3.slug

TestFilters(['Booking Engine'], () => {

describe('Show Cancellation Policy as anonymous guest', () => {
    before('only clear cookies and local storage', () => {
        
        cy.request(`automation/tests/reseedHotel/${hotelSlug}`)
        cy.clearCookies();
        cy.clearLocalStorage();
    })

    it('Access Cancellation Policy as anonymous guest', () => {
        yourStayPage.open(hotelSlug)
        footerComponent.clickCancellationPolicyButton()
        cancellationPolicyModal.assertContentIsCorrect(hotelSlug)
    });

    afterEach(function () {
        cy.fn_afterEachJira(this.currentTest)
    })
})
})
