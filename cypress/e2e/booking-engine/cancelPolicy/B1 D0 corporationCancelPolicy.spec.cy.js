import TestFilters from '../../../support/filterTests';
import accounts from '../../../fixtures/accounts';
import { header } from '../../../support/pageObjectModel/Components/header';
import {cancellationPolicyModal} from '../../../support/pageObjectModel/Components/cancellationPolicyModal';
import {yourStayPage} from '../../../support/pageObjectModel/Pages/yourStay';
import {footerComponent} from '../../../support/pageObjectModel/Components/footer';

const hotelSlug = accounts.cypressHotel3.slug

TestFilters(['Booking Engine'], () => {

describe('Show Cancellation Policy as a corporation', () => {
    before('log in as corporation', () => {

        cy.request(`automation/tests/reseedHotel/${hotelSlug}`)
        
        cy.clearCookies();
        cy.clearLocalStorage();
        cy.fn_login('corporation', accounts.cypressHotel3.corporation.email, accounts.cypressHotel3.corporation.password, accounts.cypressHotel3.slug)
        //verify user is logged in as corporation 
        header.assertLoginButtonText(accounts.cypressHotel3.corporation.name)
    })

    it('Access Cancellation Policy as a corporation', () => {
        yourStayPage.open(hotelSlug)
        footerComponent.clickCancellationPolicyButton()
        cancellationPolicyModal.assertContentIsCorrect(hotelSlug)
    });

    afterEach(function () {
        cy.fn_afterEachJira(this.currentTest)
    })
})
})
