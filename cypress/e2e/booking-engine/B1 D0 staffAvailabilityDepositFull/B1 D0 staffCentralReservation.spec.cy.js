
import TestFilters from "../../../support/filterTests";
import accounts from "../../../fixtures/accounts";
import { centralReservationsPage } from "../../../support/pageObjectModel/Pages/centralReservations";
import { extrasPage } from "../../../support/pageObjectModel/Pages/extras";
import { addDaysToDate } from "../../../support/functions/addDaysToDate";
import { guestDetailsPage } from "../../../support/pageObjectModel/Pages/guestDetails";
import { guestDetails } from "../../../fixtures/guestDetails";
import { payPage } from "../../../support/pageObjectModel/Pages/pay";
import { validJudoPayCards } from "../../../fixtures/cards/paymentDetailsJudopay";
import { bookingHubPage } from "../../../support/pageObjectModel/Pages/bookingHub";
import { basketComponent } from "../../../support/pageObjectModel/Components/basket";
import { calendarPage } from "../../../support/pageObjectModel/Pages/calendar";
import { gridPage } from "../../../support/pageObjectModel/Pages/grid";
import { hotelSettingsPage } from "../../../support/pageObjectModel/Pages/hotelSettings";
import { pmsGeneral } from "../../../support/pageObjectModel/Components/pmsGeneral";
import { paymentIframe } from "../../../support/pageObjectModel/Components/paymentIFrame";

//only able to run in local environment - requires group hotel
const hotelSlug = accounts.cardiff.slug
const groupSlug = 'hls'
const hotelName = accounts.cardiff.name
const date = cy.helpers.dateYMD();
const checkInDate = addDaysToDate(1);
const checkOutDate = addDaysToDate(2);
const finalExpectedPrice = 150;
const card = validJudoPayCards.frictionlessSuccess

TestFilters(['Booking Engine'], () => {

describe.skip("Create Booking through Central Reservations", function() {
    
    before(() => {
        cy.request('automation/tests/reseed')
        //send manual override
        cy.fn_login('staff', Cypress.env('staff_email'), Cypress.env('staff_password'))
        hotelSettingsPage.open(hotelSlug)
                         .clickResendManualOverrideButton()
    })
    
    it("Create Booking through Central Reservations", function() {

        let formatter = Intl.NumberFormat('en-GB', {style: 'currency', currency: 'GBP'});

        cy.on('window:before:load', (win) => {
            cy.stub(win, 'open').callsFake(url => {
                win.location.href = url;
            });
        });

        centralReservationsPage.open(groupSlug)
        centralReservationsPage.clickDatePicker()
        centralReservationsPage.selectDate(checkInDate)
                               .selectDate(checkOutDate)
        centralReservationsPage.clickAvailabilitySearchButton()
        centralReservationsPage.clickHotelSelectButton(hotelName)
        cy.get(centralReservationsPage.selectors.doubleRoomAddButton).click()
        centralReservationsPage.clickBookNowButton()

        // let checkAvailabilityButtonSelector = centralReservationsPage.selectors.checkAvailabilityButton(hotelSlug);

        // cy.get(checkAvailabilityButtonSelector).invoke('removeAttr', 'target').click();

        extrasPage.assertURL(hotelSlug)
        // basketComponent.assertSelectedStayDateRange(checkInDate, checkOutDate)
        cy.get(basketComponent.selectors.basketTotal).should('exist');
        cy.get(basketComponent.selectors.basketTotal).should('contain', formatter.format(finalExpectedPrice));

        extrasPage.clickContinueButton();

        // Fill the guest details form and move on to the pay page
        cy.get(guestDetailsPage.selectors.yourDetailsBox).should('exist');
        cy.get(guestDetailsPage.selectors.guestDetailsForm)
            .fillBookingEngineGuestDetailsForm(guestDetails);

        guestDetailsPage.clickContinueButton();

        payPage.assertURL()
        paymentIframe.fillJudoPayDetails(card)

        // Ensure that we reach the booking hub page and it contains the correct information
        bookingHubPage.assertURL()
        bookingHubPage.assertTotalAmount(formatter.format(finalExpectedPrice).slice(1));
        bookingHubPage.assertBalanceAmount(150.00)
        .assertPaymentAmount(0.00)
        .assertCorrectCreditCardAttached(card)
        bookingHubPage.assertGuestDetails(guestDetails);
        bookingHubPage.assertBookingDates(checkInDate, checkOutDate);

        // Ensure that the booking appears correctly on the calendar
        cy.get(bookingHubPage.selectors.bookingReference).then(($element) => {
            let bookingReference = $element.text().replace(/\W/g,'');
            calendarPage.open(hotelSlug, date)
            calendarPage.initCalendar();
            calendarPage.assertBookingExists(bookingReference);
        });

        // Ensure that the availability appears correctly on the rates grid
        // gridPage.open(hotelSlug)
        //         .assertNoAvailability(gridPage.cypressHotel6Selectors.doubleRoomAvailabilityNextDay)

    })

    afterEach(function () {
        cy.fn_afterEachJira(this.currentTest)
    })
})
})