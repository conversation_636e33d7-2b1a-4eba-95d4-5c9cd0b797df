
import TestFilters from '../../../support/filterTests';
import accounts from '../../../fixtures/accounts';
import {eventCalendarPage} from '../../../support/pageObjectModel/Pages/eventCalendar';
import {conferenceCreationModal} from '../../../support/pageObjectModel/Components/conferenceCreationModal';

const hotelSlug = accounts.cypressHotel6.slug
const name = accounts.cypressHotel6.guest.name
const email = accounts.cypressHotel6.guest.email


TestFilters(['Booking Engine'], () => {
describe('Create event booking as staff for existing guest', () => {
    before('log in as staff', () => {
        cy.request(`automation/tests/reseedHotel/${hotelSlug}`)
        cy.fn_login('staff', Cypress.env('staff_email'), Cypress.env('staff_password'))
    })

    it('Access event calendar as staff and create booking for existing guest', () => {

        const calendarEntry = `${name}`;
        eventCalendarPage
            .open(hotelSlug)
            .assertNotExists(calendarEntry)
            .clickToday()

        conferenceCreationModal
            .clickExistingGuest()
            .fillGuestEmail(email)
            .clickConfirmButton()
            .clickOkButton()

        eventCalendarPage
            .assertExists(calendarEntry)
    })

    afterEach(function () {
        cy.fn_afterEachJira(this.currentTest)
    })
})
})
