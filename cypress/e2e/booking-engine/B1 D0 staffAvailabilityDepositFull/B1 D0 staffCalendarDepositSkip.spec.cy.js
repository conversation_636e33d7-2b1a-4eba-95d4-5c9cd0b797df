import TestFilters from '../../../support/filterTests';
import accounts from '../../../fixtures/accounts';
import { guestDetails } from "../../../fixtures/guestDetails";
import { calendarPage } from "../../../support/pageObjectModel/Pages/calendar";
import { guestDetailsPage } from "../../../support/pageObjectModel/Pages/guestDetails";
import { payPage } from "../../../support/pageObjectModel/Pages/pay";
import { bookingHubPage } from "../../../support/pageObjectModel/Pages/bookingHub";
import { gridPage } from "../../../support/pageObjectModel/Pages/grid";

const hotelSlug = accounts.cypressHotel6.slug;
const date = cy.helpers.dateYMD()
const expectedDepositText = '100.00';
const balanceAmount = '100.00'
const paymentStatus = 'pending'

TestFilters(['Booking Engine'], () => {

describe ('Create a booking on the calendar as a staff member which uses a 100% deposit rate plan, but skip entering payment details', () => {
    it('Create a booking on the calendar as a staff member which uses a 100% deposit rate plan, but skip entering payment details', {}, () => {

        cy.request(`automation/tests/reseedHotel/${hotelSlug}`)
        cy.fn_login('staff', Cypress.env('staff_email'), Cypress.env('staff_password'));
        
        calendarPage.open(hotelSlug, date)

        calendarPage.initCalendar();

        calendarPage.assertRoomTypeHasName('sng', 'Single Room');

        calendarPage.clickRoomNumberCurrentDay(2)

        let reservation = {
            inventory: 'Base Rate @ £100.00',
            roomType: 'Single',
            adults: 1,
            reservationType: 'New Guest'
        };

        cy.get(calendarPage.selectors.bookingCreationModalForm).should('be.visible')
            .fillCalendarModal(reservation);


        guestDetailsPage.assertURL(hotelSlug)

        cy.get(guestDetailsPage.selectors.yourDetailsBox).should('exist');

        cy.get(guestDetailsPage.selectors.guestDetailsForm)
            .fillBookingEngineGuestDetailsForm(guestDetails);

        guestDetailsPage.clickContinueButton();

        payPage.assertURL()
        payPage.assertDepositPaymentMessage(expectedDepositText);
        payPage.assertSkipPaymentMessage()
        payPage.skipPayment();

        // Once redirected to the booking page, confirm that the details are correct.
        bookingHubPage.assertURL()

        bookingHubPage.assertGuestDetails(guestDetails);
        bookingHubPage.assertTotalAmount(expectedDepositText);
        bookingHubPage.assertBalanceAmount(balanceAmount);
        bookingHubPage.assertPaymentStatus(paymentStatus);

        cy.get(bookingHubPage.selectors.bookingReference).then(($element) => {
            // Ensure that the booking exists on the calendar
            let bookingReference = $element.text().replace(/\W/g,'');
            calendarPage.open(hotelSlug, date)
            calendarPage.initCalendar();
            calendarPage.assertBookingExists(bookingReference);
        });

        // Ensure that the availability appears correctly on the rates grid
        // gridPage.open(hotelSlug)
        // cy.get(gridPage.cypressHotel6Selectors.singleRoomAvailabilityNextDay).should('contain', '1');
    })

    afterEach(function () {
        cy.fn_afterEachJira(this.currentTest)
    })
})
})