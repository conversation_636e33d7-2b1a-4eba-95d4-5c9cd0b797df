import TestFilters from "../../../support/filterTests";
import accounts from "../../../fixtures/accounts";
import { yourStayPage } from "../../../support/pageObjectModel/Pages/yourStay";
import { extrasPage } from "../../../support/pageObjectModel/Pages/extras";
import { guestDetailsPage } from "../../../support/pageObjectModel/Pages/guestDetails";
import { payPage } from "../../../support/pageObjectModel/Pages/pay";
import { bookingHubPage } from "../../../support/pageObjectModel/Pages/bookingHub";
import { gridPage } from "../../../support/pageObjectModel/Pages/grid";
import { calendarPage } from "../../../support/pageObjectModel/Pages/calendar";
import { pmsGeneral } from "../../../support/pageObjectModel/Components/pmsGeneral";

import { guestDetails } from "../../../fixtures/guestDetails";
import { dateConverter } from "../../../support/functions/formatDate";
import { addDaysToDate } from "../../../support/functions/addDaysToDate";
import { dashboardPage } from "../../../support/pageObjectModel/Pages/dashboard";
import { bookingsPage } from "../../../support/pageObjectModel/Pages/bookings";

TestFilters(['Booking Engine'], () => {

const hotelSlug = accounts.cypressHotel6.slug
const roomSelector = yourStayPage.selectors.doubleRoomBox
const date = cy.helpers.dateYMD();
const currentDateFormatted = dateConverter(date)
const nextDayDate = addDaysToDate(1)

describe('Create booking as staff with full deposit', () => {
    before('log in as staff', () => {   
    
    cy.request(`automation/tests/reseedHotel/${hotelSlug}`)
    cy.fn_login('staff', Cypress.env('staff_email'), Cypress.env('staff_password'))
    })

    it('Access booking engine as staff and create booking', () => {
    yourStayPage.open(hotelSlug)
   
    yourStayPage
          .clickSelectDates()
          .selectCurrentDate()
          .selectNextDayDate()
          .clickSearchButton()
          .clickOccupancySearchButton()

    //add 'double room basic rate' rate plan
    cy.get(roomSelector + ' ' + yourStayPage.selectors.addRatePlanButton).should('exist');
    cy.get(roomSelector + ' ' + yourStayPage.selectors.addRatePlanButton).eq(0).click();
    cy.get(pmsGeneral.selectors.spinner).should('not.be.visible');
    yourStayPage.clickContinueButton()
    extrasPage.clickContinueButton()

    guestDetailsPage
          .fillInGuestBasicInfo(guestDetails)
          .selectPostcodeLookupButton()
          .selectFirstAddressFromDropdown()
          .clickContinueButton()

    payPage.assertDepositPaymentMessage('75.00')
    payPage
          .assertSkipPaymentMessage()
          .skipPayment()
    
    //post booking checks
    //confirm booking is captured and accurate on the booking hub
    bookingHubPage
          .assertPaymentStatus('pending')
          .clickExpandBookingButton()
    cy.get(bookingHubPage.selectors.reservationDetails).invoke('text')
    .then((text) => {
      const normalizedText = text.replace(/\s+/g, ' ').trim();
      expect(normalizedText).to.include('Base Rate : Double Room')
                            .to.include('£150.00')
                            .to.include(`${currentDateFormatted} £150.00`)
    })
    
    bookingHubPage
            .assertPaymentAmount(0.00)
            .assertBalanceAmount(150.00)
            .assertGuestDetails(guestDetails)
            .assertBookingDates(date, nextDayDate)
   
   cy.get(bookingHubPage.selectors.bookedDate).should('contain', `${currentDateFormatted}` )
     

    cy.get(bookingHubPage.selectors.bookingReference).invoke('text').then((text) => {
      const bookingRef = text.replace(/\s+/g, ' ').trim();
    //verify booking is displayed on the dashboard
     dashboardPage.open(hotelSlug)
     cy.contains(bookingRef).should('be.visible')

    // //verify booking is visible on the bookings page 
    bookingsPage.open(hotelSlug)
     cy.contains(bookingRef).should('be.visible')
     cy.contains(bookingRef).click()

//      gridPage
//      .open(hotelSlug)
//      gridPage.assertNoAvailability(gridPage.cypressHotel6Selectors.doubleRoomAvailabilityCurrentDay)


    calendarPage
          .open(hotelSlug, date)
          .assertBookingExists(bookingRef)
    })
    });

    afterEach(function () {
      cy.fn_afterEachJira(this.currentTest)
    })
})
})
    
