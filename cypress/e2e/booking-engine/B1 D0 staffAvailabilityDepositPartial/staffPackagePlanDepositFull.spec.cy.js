import TestFilters from "../../../support/filterTests";
import accounts from "../../../fixtures/accounts";
import { yourStayPage } from "../../../support/pageObjectModel/Pages/yourStay";
import { extrasPage } from "../../../support/pageObjectModel/Pages/extras";
import { guestDetailsPage } from "../../../support/pageObjectModel/Pages/guestDetails";
import { payPage } from "../../../support/pageObjectModel/Pages/pay";
import { bookingHubPage } from "../../../support/pageObjectModel/Pages/bookingHub";
import { gridPage } from "../../../support/pageObjectModel/Pages/grid";
import { calendarPage } from "../../../support/pageObjectModel/Pages/calendar";

import { validJudoPayCards } from "../../../fixtures/cards/paymentDetailsJudopay";
import { guestDetails } from "../../../fixtures/guestDetails";
import { dateConverter } from "../../../support/functions/formatDate";
import { addDaysToDate } from "../../../support/functions/addDaysToDate";
import { bookingsPage } from "../../../support/pageObjectModel/Pages/bookings";
import { dashboardPage } from "../../../support/pageObjectModel/Pages/dashboard";
import { paymentIframe } from "../../../support/pageObjectModel/Components/paymentIFrame";

const date = cy.helpers.dateYMD();
const currentDateFormatted = dateConverter(date)
const nextDayDate = addDaysToDate(1)
const hotelSlug = accounts.cypressHotel11.slug
const packageTitle = 'Breakfast Only'
const card = validJudoPayCards.frictionlessSuccess

TestFilters(['Booking Engine'], () => {

describe('Create booking as staff with full deposit', () => {
    before('log in as staff', () => {   
    
    cy.request(`automation/tests/reseedHotel/${hotelSlug}`)

    cy.fn_login('staff', Cypress.env('staff_email'), Cypress.env('staff_password'))
     })

    it('Access booking engine as staff and create booking', () => {
    yourStayPage.open(hotelSlug)
    yourStayPage.clickSelectDates()
    yourStayPage.selectCurrentDate()
    yourStayPage.selectNextDayDate()
                .clickSearchButton()
                .clickOccupancySearchButton()
    
    yourStayPage.assertRatePlanListed(packageTitle)
    yourStayPage.addRatePlanByName(packageTitle)
    .basketComponent.clickContinueButton()

 
    yourStayPage.clickContinueButton()
    extrasPage.basketComponent.clickContinueButton()

    guestDetailsPage.fillInGuestBasicInfo(guestDetails)
    guestDetailsPage.selectPostcodeLookupButton()
    guestDetailsPage.selectFirstAddressFromDropdown()
    guestDetailsPage.clickContinueButton()
   
    payPage.assertDepositPaymentMessage('75.00')
    paymentIframe.fillJudoPayDetails(card)
  
    //post booking checks
    //confirm booking is captured and accurate on the booking hub
    cy.get(bookingHubPage.selectors.paymentStatusHeader, {timeout: 20000}).should('exist')
    bookingHubPage.assertPaymentStatus('paid')
    bookingHubPage.clickExpandBookingButton()
    cy.get(bookingHubPage.selectors.reservationDetails).invoke('text')
    .then((text) => {
      const normalizedText = text.replace(/\s+/g, ' ').trim();
      expect(normalizedText).to.include('Breakfast Only : Double Room')
                            .to.include('£75.00')
                            .to.include(`${currentDateFormatted} £75.00`)
    })
    
    bookingHubPage.assertPaymentAmount(75.00)
                  .assertBalanceAmount(0.00)
                  .assertGuestDetails(guestDetails)
                  .assertBookingDates(date, nextDayDate)
   cy.get(bookingHubPage.selectors.bookedDate).should('contain', `${currentDateFormatted}` )
   bookingHubPage.assertCorrectCreditCardAttached(card)

    cy.get(bookingHubPage.selectors.bookingReference).invoke('text').then((text) => {
      const bookingRef = text.replace(/\s+/g, ' ').trim();
    //verify booking is displayed on the dashboard
    dashboardPage.open(hotelSlug)
                 .assertBookingListed(bookingRef)

    // //verify booking is visible on the bookings page 
    bookingsPage.open(hotelSlug)
                .assertBookingListed(bookingRef)
    
    //Assert calendar entry is displayed
    calendarPage.open(hotelSlug, date)
    calendarPage.assertBookingExists(bookingRef)

    // //Verify booking is displayed on the payment report 
    cy.fn_safeVisit(`/hotels/${hotelSlug}/reports/invoice/payment?date=${date}`)
    cy.contains(bookingRef).should('be.visible')
    })

    //Verify accurate rates grid availability 
    gridPage.open(hotelSlug)
            .assertNoAvailability(gridPage.cypressHotel11Selectors.doubleRoomAvailabilityCurrentDay)
    })

    afterEach(function () {
      cy.fn_afterEachJira(this.currentTest)
    })
})
}) 
