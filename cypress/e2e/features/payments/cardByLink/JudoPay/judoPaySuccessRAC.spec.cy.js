import TestFilters from "../../../../support/filterTests";
import accounts from "../../../../fixtures/accounts";
import { bookingsPage } from "../../../../support/pageObjectModel/Pages/bookings";
import { bookingHubPage } from "../../../../support/pageObjectModel/Pages/bookingHub";
import { guestProfilePage } from "../../../../support/pageObjectModel/Pages/guestProfile";
import { pmsGeneral } from "../../../../support/pageObjectModel/Components/pmsGeneral";

import { validJudoPayCards } from "../../../../fixtures/cards/paymentDetailsJudopay";
import { cardByLinkForm } from "../../../../support/pageObjectModel/Pages/cardRequestForm";
import { cardByLinkPage } from "../../../../support/pageObjectModel/Pages/cardByLinkPage";
import { paymentIframe } from "../../../../support/pageObjectModel/Components/paymentIFrame";

const hotelSlug = accounts.cypressHotel6.slug
const hotelName = accounts.cypressHotel6.name
const card = validJudoPayCards.frictionlessSuccess

TestFilters(['Booking Engine'], () => {

describe('Payment Processing: Request a Card By Link as staff', () => {
 
    before('can log in', () => {
        
        cy.request(`automation/tests/reseedHotel/${hotelSlug}`)

        cy.fn_login('staff', Cypress.env('staff_email'), Cypress.env('staff_password'))
        cy.get(pmsGeneral.selectors.messageSuccess).should('contain', 'Logged in');
    })
 
    it('Verify guest can successfully add card via request and card is added to associated booking and visible in the relevant guest profile', () => {
        bookingsPage.open(hotelSlug)
        bookingsPage.clickFirstExistingBooking()
        bookingHubPage.assertCreditCardNotAttached()
        bookingHubPage.clickAddCardButton()
 
        bookingHubPage.selectCardByLinkOption()
        cardByLinkForm.verifyHeader()
        cardByLinkForm.submitAndOpenRAC()

        cy.get(cardByLinkPage.selectors.headerTitle).should('contain', 'Card Request')
        cy.get(cardByLinkPage.selectors.formTitle).should('contain', 'Secure card request')
        cy.get(cardByLinkPage.selectors.formMessage).invoke('text').then((text) => {
            const normalizedText = text.replace(/\s+/g, ' ').trim();
            expect(normalizedText).to.include(`To make your stay with us at ${hotelName} more convenient, we would like to keep a credit card on file during your visit. This will allow you to enjoy a hassle-free check-in and check-out experience and charge any incidental expenses directly to your room. To view the invoice that the card will be associated with, please select the dropdown option below:`)
          })
        cy.get(cardByLinkPage.selectors.invoiceDetailsChevron).should('contain', 'Your Invoice')
        paymentIframe.fillJudoPayDetails(card)
        cardByLinkPage.assertSuccessfulCardAddition(card)

        //Verify card is displayed on booking hub 
        cy.fn_login('staff', Cypress.env('staff_email'), Cypress.env('staff_password'))
        bookingsPage.open(hotelSlug)
        bookingsPage.clickFirstExistingBooking()
        bookingHubPage.assertCorrectCreditCardAttached(card)

        //Verify card is displayed on guest profile
        bookingHubPage.clickGuestProfileButton()
        guestProfilePage.assertCardIsVisible(card)
        
    })

    afterEach(function () {
        cy.fn_afterEachJira(this.currentTest)
    })
    
})
})