import TestFilters from "../../../../../support/filterTests";
import accounts from "../../../../../fixtures/accounts";
import { bookingsPage } from "../../../../../support/pageObjectModel/Pages/bookings";
import { bookingHubPage } from "../../../../../support/pageObjectModel/Pages/bookingHub";
import { guestProfilePage } from "../../../../../support/pageObjectModel/Pages/guestProfile";
import { pmsGeneral } from "../../../../../support/pageObjectModel/Components/pmsGeneral";

import { invalidJudoPayCards } from "../../../../../fixtures/cards/paymentDetailsJudopay";
import { cardByLinkForm } from "../../../../../support/pageObjectModel/Pages/cardRequestForm";
import { cardByLinkPage } from "../../../../../support/pageObjectModel/Pages/cardByLinkPage";
import { paymentIframe } from "../../../../../support/pageObjectModel/Components/paymentIFrame";

TestFilters(['Booking Engine'], () => {

const card = invalidJudoPayCards.invalidFrictionlessSuccessictionlessSuccess
const hotelSlug = accounts.cypressHotel6.slug

describe('Payment Processing: Request a Card By Link as staff', () => {
 
    before('can log in', () => {
        
        cy.request(`automation/tests/reseedHotel/${hotelSlug}`)

        cy.fn_login('staff', Cypress.env('staff_email'), Cypress.env('staff_password'))
        cy.get(pmsGeneral.selectors.messageSuccess).should('contain', 'Logged in');
    })
 
    it('Verify guest can unsuccessfully add card via request and card is not added to associated booking and not visible in the relevant guest profile', () => {
        // view a booking
        bookingsPage.open(hotelSlug)
        bookingsPage.clickFirstExistingBooking()
        bookingHubPage.assertCreditCardNotAttached()
        bookingHubPage.clickAddCardButton()
 
        // click 'Request a payment: Take a payment by link' item
        bookingHubPage.selectCardByLinkOption()
        cardByLinkForm.verifyHeader()
        cardByLinkForm.submitAndOpenRAC()

        paymentIframe.fillJudoPayDetails(card)

        // //verify payment unsuccessful message -- needs confirmation with matt
        cardByLinkPage.assertUnsuccessfulCardAddition()

        //Verify card is displayed on booking hub 
        cy.fn_login('staff', Cypress.env('staff_email'), Cypress.env('staff_password'))
        bookingsPage.open(hotelSlug)
        bookingsPage.clickFirstExistingBooking()
        bookingHubPage.assertCreditCardNotAttached()

        //Verify card is displayed on guest profile - needs data attribute 
        bookingHubPage.clickGuestProfileButton()
        cy.get(guestProfilePage.selectors.cardBox).should('not.exist')
        
    })

    afterEach(function () {
        cy.fn_afterEachJira(this.currentTest)
    })
    
})
})