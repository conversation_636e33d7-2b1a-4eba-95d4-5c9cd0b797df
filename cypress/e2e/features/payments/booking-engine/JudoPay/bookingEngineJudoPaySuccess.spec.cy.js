import accounts from "../../../../fixtures/accounts";
import TestFilters from "../../../../support/filterTests";
import { yourStayPage } from "../../../../support/pageObjectModel/Pages/yourStay";
import { extrasPage } from "../../../../support/pageObjectModel/Pages/extras";
import { guestDetailsPage } from "../../../../support/pageObjectModel/Pages/guestDetails";
import { payPage } from "../../../../support/pageObjectModel/Pages/pay";
import { thankYouPage } from "../../../../support/pageObjectModel/Pages/thankYou";
import { pmsGeneral } from "../../../../support/pageObjectModel/Components/pmsGeneral";

import { guestDetails } from "../../../../fixtures/guestDetails";
import { paymentIframe } from "../../../../support/pageObjectModel/Components/paymentIFrame";
import { validJudoPayCards } from "../../../../fixtures/cards/paymentDetailsJudopay";

const hotelSlug = accounts.cypressHotel6.slug
const roomSelector = yourStayPage.selectors.familyRoomBox

TestFilters(['P1'], () => {
for (const [cardName, cardDetails] of Object.entries(validJudoPayCards)) {
describe(`Create booking with a Judopay card: ${cardName}`, () => {
    before('Run seed and clear session', () => {  
    cy.request(`automation/tests/reseedHotel/${hotelSlug}`)
    cy.clearCookies();
    cy.clearLocalStorage();
    })

    it(`Create booking with: ${cardName} `, () => {
    yourStayPage.open(hotelSlug)
    yourStayPage.clickSelectDates()
    yourStayPage.selectCurrentDate()
    yourStayPage.selectNextDayDate()
                .clickSearchButton()
    yourStayPage.clickOccupancySearchButton()
    
    //add family room base rate plan
    cy.get(roomSelector + ' ' + yourStayPage.selectors.addRatePlanButton).should('exist');
    cy.get(roomSelector + ' ' + yourStayPage.selectors.addRatePlanButton).eq(0).click()
    cy.get(pmsGeneral.selectors.spinner).should('not.be.visible');
    
    yourStayPage.clickContinueButton(); 
    extrasPage.clickContinueButton()

    //enter guest details
    guestDetailsPage.fillInGuestBasicInfo(guestDetails)
    guestDetailsPage.selectPostcodeLookupButton()
    guestDetailsPage.selectFirstAddressFromDropdown()
    guestDetailsPage.clickContinueButton()

    payPage.assertNoDepositMessage()
    paymentIframe.fillJudoPayDetails(cardDetails)
    payPage.submitCardDetails()

    thankYouPage.assertURL()
  
   })  

   afterEach(function () {
    cy.fn_afterEachJira(this.currentTest)
    })
  })
}
});  
    