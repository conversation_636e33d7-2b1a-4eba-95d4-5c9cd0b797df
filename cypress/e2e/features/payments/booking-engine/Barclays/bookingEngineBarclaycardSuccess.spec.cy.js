import TestFilters from "../../../../../support/filterTests";
import { yourStayPage } from "../../../../../support/pageObjectModel/Pages/yourStay";
import { extrasPage } from "../../../../../support/pageObjectModel/Pages/extras";
import { guestDetailsPage } from "../../../../../support/pageObjectModel/Pages/guestDetails";
import { payPage } from "../../../../../support/pageObjectModel/Pages/pay";
import { thankYouPage } from "../../../../../support/pageObjectModel/Pages/thankYou";
import { pmsGeneral } from "../../../../../support/pageObjectModel/Components/pmsGeneral";
import { guestDetails } from "../../../../../fixtures/guestDetails";
import { paymentIframe } from "../../../../../support/pageObjectModel/Components/paymentIFrame";
import { validBarclaysCards } from "../../../../../fixtures/cards/paymentDetailsBarclays";

const hotelSlug = 'auto'
// this test requires a seeded hotel with a barclaycard gateway. Auto is a hotel currenlt on UAT, please run this spec in the UAT environment
// currently fails due to barclaycard error
const roomSelector = '.bordered:contains("Double")'

TestFilters(['P1'], () => {
for (const [cardName, cardDetails] of Object.entries(validBarclaysCards)) {
describe(`Create booking with a Barclays card: ${cardName}`, () => {
    before('Clear Session', () => {   
    // cy.request(`automation/tests/reseedHotel/${hotelSlug}`)
    cy.clearCookies();
    cy.clearLocalStorage();
    })

    it(`Create booking with : ${cardName}`, () => {
    yourStayPage.open(hotelSlug)
    yourStayPage.clickSelectDates()
    yourStayPage.selectCurrentDate()
    yourStayPage.selectNextDayDate()
                .clickSearchButton()
    yourStayPage.clickOccupancySearchButton()
    
    cy.get(roomSelector + ' ' + yourStayPage.selectors.addRatePlanButton).should('exist');
    cy.get(roomSelector + ' ' + yourStayPage.selectors.addRatePlanButton).eq(0).click()
    cy.get(pmsGeneral.selectors.spinner).should('not.be.visible');
    
    yourStayPage.clickContinueButton(); 
    extrasPage.clickContinueButton()

    //enter guest details
    guestDetailsPage.fillInGuestBasicInfo(guestDetails)
    guestDetailsPage.selectPostcodeLookupButton()
    guestDetailsPage.selectFirstAddressFromDropdown()
    guestDetailsPage.clickContinueButton()

    payPage.assertURL()
    paymentIframe.fillBarclaysDetails(cardDetails)

    thankYouPage.assertURL()
  
   })   

   afterEach(function () {
    cy.fn_afterEachJira(this.currentTest)
    })
  })
}
})


    