import TestFilters from '../../../../../../support/filterTests';
import accounts from '../../../../../../fixtures/accounts';
import { payByLinkForm } from '../../../../../../support/pageObjectModel/Pages/paymentRequestForm';
import { payByLinkPage } from '../../../../../../support/pageObjectModel/Pages/payByLinkPage';
import { bookingsPage } from '../../../../../../support/pageObjectModel/Pages/bookings';
import { bookingHubPage } from '../../../../../../support/pageObjectModel/Pages/bookingHub';
import { guestProfilePage } from '../../../../../../support/pageObjectModel/Pages/guestProfile';

import { validJudoPayCards } from '../../../../../../fixtures/cards/paymentDetailsJudopay';
import { pmsGeneral } from '../../../../../../support/pageObjectModel/Components/pmsGeneral';
import { paymentIframe } from '../../../../../../support/pageObjectModel/Components/paymentIFrame';

const card = validJudoPayCards.frictionlessSuccess
const hotelSlug = accounts.cypressHotel6.slug
const hotelName = accounts.cypressHotel6.name
const paymentAmount = 10.00
const paymentAmountFormatted = paymentAmount.toLocaleString('en-US', {
    style: 'currency',
    currency: 'GBP'
  });
const bookingAmount = 200.00
const balanceAmount = bookingAmount - paymentAmount

TestFilters(['Booking Engine'], () => {

describe('Payment Processing: Request a Payment By Link as staff', () => {
 
    before('can log in', () => {

        cy.request(`automation/tests/reseedHotel/${hotelSlug}`)

        cy.fn_login('staff', Cypress.env('staff_email'), Cypress.env('staff_password'))
        cy.get(pmsGeneral.selectors.messageSuccess).should('contain', 'Logged in');
    })
 
    it('Verify user can successfully complete a Payment By Link with a new card', () => {
        // view a booking
        bookingsPage.open(hotelSlug)
        bookingsPage.clickFirstExistingBooking()
        bookingHubPage.assertPaymentStatus('pending')
        bookingHubPage.assertCreditCardNotAttached()
        bookingHubPage.clickAddPaymentButton()
 
        bookingHubPage.selectPaymentByLinkOption()
        payByLinkForm.assertHeader()
 
        payByLinkForm.submitAndOpenPBL(paymentAmount)
        
        
        cy.get(payByLinkPage.selectors.headerTitle).should('contain', 'Payment Request')
        cy.get(payByLinkPage.selectors.formTitle).should('contain', 'Secure payment request')
        payByLinkPage.assertFormMessage(hotelName, paymentAmountFormatted)
        cy.get(payByLinkPage.selectors.invoiceDetailsChevron).should('contain', 'Your Invoice')
        
        paymentIframe.fillJudoPayDetails(card)
        
        payByLinkPage.assertSuccessfulPayment(paymentAmountFormatted)

        //Verify payment is reflected on booking hub 
        cy.fn_login('staff', Cypress.env('staff_email'), Cypress.env('staff_password'))
        bookingsPage.open(hotelSlug)
        bookingsPage.clickFirstExistingBooking()
        bookingHubPage
            .assertPaymentStatus('part-paid')
            .assertPaymentAmount(paymentAmount)
            .assertBalanceAmount(balanceAmount)
            .assertCorrectCreditCardAttached(card)
            .clickGuestProfileButton()
        
        guestProfilePage.assertCardIsVisible(card)
    })

    afterEach(function () {
        cy.fn_afterEachJira(this.currentTest)
    })

})
})
 